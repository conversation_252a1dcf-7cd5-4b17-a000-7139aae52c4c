package de.dasjeff.aSMPVCore.messaging;

import org.jetbrains.annotations.NotNull;

import java.util.Set;

/**
 * Interface for handling incoming network messages.
 * Implementations of this interface can register to receive specific message types.
 */
public interface MessageHandler {
    
    /**
     * Gets the set of message types this handler can process.
     * @return A set of message types this handler is interested in.
     */
    @NotNull
    Set<MessageType> getHandledMessageTypes();
    
    /**
     * Handles an incoming network message.
     * This method should be implemented to process the specific message types
     * returned by {@link #getHandledMessageTypes()}.
     * 
     * @param message The incoming network message.
     * @return true if the message was handled successfully, false otherwise.
     */
    boolean handleMessage(@NotNull NetworkMessage message);
    
    /**
     * Gets the priority of this handler.
     * Handlers with higher priority will be called first for the same message type.
     * 
     * @return The handler priority (1-10, where 10 is highest priority).
     */
    default int getHandlerPriority() {
        return 5; // Default medium priority
    }
    
    /**
     * Gets the name of this handler for logging and debugging purposes.
     * @return A descriptive name for this handler.
     */
    @NotNull
    String getHandlerName();
    
    /**
     * Called when the handler is registered with the messaging system.
     * This can be used for initialization that requires the messaging system to be available.
     */
    default void onRegister() {
        // Default implementation does nothing
    }
    
    /**
     * Called when the handler is unregistered from the messaging system.
     * This can be used for cleanup when the handler is no longer needed.
     */
    default void onUnregister() {
        // Default implementation does nothing
    }
    
    /**
     * Checks if this handler should process messages from the specified source server.
     * This can be used to filter messages based on the source server.
     * 
     * @param sourceServer The name of the server that sent the message.
     * @return true if messages from this server should be processed, false otherwise.
     */
    default boolean shouldProcessFromServer(@NotNull String sourceServer) {
        return true; // Default: process messages from all servers
    }
    
    /**
     * Checks if this handler can process messages of the specified age.
     * This can be used to ignore old messages that are no longer relevant.
     * 
     * @param messageAge The age of the message in milliseconds.
     * @return true if the message should be processed, false if it's too old.
     */
    default boolean shouldProcessMessageAge(long messageAge) {
        return messageAge < 30000; // Default: ignore messages older than 30 seconds
    }
    
    /**
     * Called when a message handling operation fails.
     * This allows handlers to implement custom error handling or recovery logic.
     * 
     * @param message The message that failed to be processed.
     * @param error The exception that occurred during processing.
     */
    default void onHandlingError(@NotNull NetworkMessage message, @NotNull Throwable error) {
        // Default implementation does nothing
    }
}
