package de.dasjeff.adventureSMPCore.modules.homesystem.database;

import de.dasjeff.adventureSMPCore.AdventureSMPCore;
import de.dasjeff.adventureSMPCore.managers.DatabaseManager;
import de.dasjeff.adventureSMPCore.modules.homesystem.HomeModule;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.Home;
import de.dasjeff.adventureSMPCore.modules.homesystem.model.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.logging.Level;

public class HomeDataAccessor {

    private final AdventureSMPCore corePlugin;
    private final DatabaseManager dbManager;
    private final int batchSize;

    private final String HOMES_TABLE_NAME = "homes";
    private final String PLAYERDATA_TABLE_NAME = "players";

    public HomeDataAccessor(AdventureSMPCore corePlugin, HomeModule homeModule) {
        this.corePlugin = corePlugin;
        this.dbManager = corePlugin.getDatabaseManager();
        this.batchSize = corePlugin.getConfigManager().getMainConfig().getInt("performance.batch_operation_size", 100);
    }

    public boolean initializeDatabase() {
        if (!dbManager.isConnected()) {
            corePlugin.getLogger().severe("[" + HomeModule.MODULE_NAME + "] Database is not connected. Cannot initialize tables.");
            return false;
        }
        try (Connection conn = dbManager.getConnection(); Statement stmt = conn.createStatement()) {
            String createHomesTableSQL = "CREATE TABLE IF NOT EXISTS " + HOMES_TABLE_NAME + " (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY," +
                    "player_uuid CHAR(36) NOT NULL," +
                    "home_name VARCHAR(50) NOT NULL COLLATE utf8mb4_general_ci," +
                    "world_uuid CHAR(36) NOT NULL," +
                    "x DOUBLE NOT NULL," +
                    "y DOUBLE NOT NULL," +
                    "z DOUBLE NOT NULL," +
                    "pitch FLOAT NOT NULL," +
                    "yaw FLOAT NOT NULL," +
                    "creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," +
                    "UNIQUE INDEX idx_player_home (player_uuid, home_name)," +
                    "INDEX idx_player_uuid (player_uuid)," +
                    "INDEX idx_creation_date (creation_date)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            stmt.executeUpdate(createHomesTableSQL);
            corePlugin.getLogger().info("[" + HomeModule.MODULE_NAME + "] Table '" + HOMES_TABLE_NAME + "' ensured.");

            String createPlayerDataTableSQL = "CREATE TABLE IF NOT EXISTS " + PLAYERDATA_TABLE_NAME + " (" +
                    "player_uuid CHAR(36) PRIMARY KEY," +
                    "last_known_name VARCHAR(16) NOT NULL COLLATE utf8mb4_general_ci," +
                    "last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," +
                    "first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                    "total_punishments INT DEFAULT 0," +
                    "last_punishment_date TIMESTAMP NULL," +
                    "INDEX idx_last_known_name (last_known_name)," +
                    "INDEX idx_last_seen (last_seen)," +
                    "INDEX idx_total_punishments (total_punishments)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            stmt.executeUpdate(createPlayerDataTableSQL);
            corePlugin.getLogger().info("[" + HomeModule.MODULE_NAME + "] Table '" + PLAYERDATA_TABLE_NAME + "' ensured.");

            return true;
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not create database tables!", e);
            return false;
        }
    }

    // --- Home Operations ---

    public boolean setHome(@NotNull Home home) {
        // Input validation
        if (!isValidHomeName(home.getHomeName()) || !isValidUUID(home.getPlayerUuid()) || !isValidUUID(home.getWorldUuid())) {
            corePlugin.getLogger().warning("[" + HomeModule.MODULE_NAME + "] Invalid data provided for setHome operation");
            return false;
        }

        String sql = "INSERT INTO " + HOMES_TABLE_NAME +
                     " (player_uuid, home_name, world_uuid, x, y, z, pitch, yaw) VALUES (?, ?, ?, ?, ?, ?, ?, ?) " +
                     "ON DUPLICATE KEY UPDATE world_uuid = VALUES(world_uuid), x = VALUES(x), y = VALUES(y), " +
                     "z = VALUES(z), pitch = VALUES(pitch), yaw = VALUES(yaw), last_modified = CURRENT_TIMESTAMP;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            pstmt.setString(1, home.getPlayerUuid().toString());
            pstmt.setString(2, home.getHomeName());
            pstmt.setString(3, home.getWorldUuid().toString());
            pstmt.setDouble(4, home.getX());
            pstmt.setDouble(5, home.getY());
            pstmt.setDouble(6, home.getZ());
            pstmt.setFloat(7, home.getPitch());
            pstmt.setFloat(8, home.getYaw());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        home.setId(generatedKeys.getInt(1));
                    }
                }
                return true;
            }
            return false;
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not set home for " + home.getPlayerUuid() + ": " + home.getHomeName(), e);
            return false;
        }
    }

    @Nullable
    public Home getHome(@NotNull UUID playerUuid, @NotNull String homeName) {
        if (!isValidUUID(playerUuid) || !isValidHomeName(homeName)) {
            return null;
        }

        String sql = "SELECT * FROM " + HOMES_TABLE_NAME + " WHERE player_uuid = ? AND home_name = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, homeName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToHome(rs);
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve home for " + playerUuid + ":" + homeName, e);
        }
        return null;
    }

    public List<Home> getHomes(@NotNull UUID playerUuid) {
        if (!isValidUUID(playerUuid)) {
            return new ArrayList<>();
        }

        List<Home> homes = new ArrayList<>();
        String sql = "SELECT * FROM " + HOMES_TABLE_NAME + " WHERE player_uuid = ? ORDER BY creation_date ASC;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    homes.add(mapResultSetToHome(rs));
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve homes for player " + playerUuid, e);
        }
        return homes;
    }

    public int getHomeCount(@NotNull UUID playerUuid) {
        if (!isValidUUID(playerUuid)) {
            return 0;
        }

        String sql = "SELECT COUNT(*) FROM " + HOMES_TABLE_NAME + " WHERE player_uuid = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not get home count for player " + playerUuid, e);
        }
        return 0;
    }

    public boolean deleteHome(@NotNull UUID playerUuid, @NotNull String homeName) {
        if (!isValidUUID(playerUuid) || !isValidHomeName(homeName)) {
            return false;
        }

        String sql = "DELETE FROM " + HOMES_TABLE_NAME + " WHERE player_uuid = ? AND home_name = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, homeName);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not delete home for " + playerUuid + ":" + homeName, e);
            return false;
        }
    }

    public int deleteAllHomes(@NotNull UUID playerUuid) {
        if (!isValidUUID(playerUuid)) {
            return 0;
        }

        String sql = "DELETE FROM " + HOMES_TABLE_NAME + " WHERE player_uuid = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not delete all homes for " + playerUuid, e);
            return 0;
        }
    }

    // --- PlayerData Operations ---

    public void updatePlayerData(@NotNull UUID playerUuid, @NotNull String playerName) {
        if (!isValidUUID(playerUuid) || !isValidPlayerName(playerName)) {
            corePlugin.getLogger().warning("[" + HomeModule.MODULE_NAME + "] Invalid data provided for updatePlayerData operation");
            return;
        }

         String sql = "INSERT INTO " + PLAYERDATA_TABLE_NAME + " (player_uuid, last_known_name, last_seen, first_seen) VALUES (?, ?, NOW(), NOW()) " +
                     "ON DUPLICATE KEY UPDATE last_known_name = VALUES(last_known_name), last_seen = NOW();";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, playerName);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not update player data for " + playerName, e);
        }
    }

    @Nullable
    public PlayerData getPlayerData(@NotNull UUID playerUuid) {
        if (!isValidUUID(playerUuid)) {
            return null;
        }

        String sql = "SELECT * FROM " + PLAYERDATA_TABLE_NAME + " WHERE player_uuid = ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerUuid.toString());
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new PlayerData(
                            UUID.fromString(rs.getString("player_uuid")),
                            rs.getString("last_known_name"),
                            rs.getTimestamp("last_seen")
                    );
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve player data for UUID " + playerUuid, e);
        }
        return null;
    }

    @Nullable
    public PlayerData getPlayerDataByName(@NotNull String playerName) {
        if (!isValidPlayerName(playerName)) {
            return null;
        }

        String sql = "SELECT * FROM " + PLAYERDATA_TABLE_NAME + " WHERE last_known_name = ? ORDER BY last_seen DESC LIMIT 1;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return new PlayerData(
                            UUID.fromString(rs.getString("player_uuid")),
                            rs.getString("last_known_name"),
                            rs.getTimestamp("last_seen")
                    );
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve player data for name " + playerName, e);
        }
        return null;
    }

    public List<String> getPlayerNamesStartingWith(@NotNull String prefix, int limit) {
        if (prefix.length() > 16 || limit <= 0 || limit > 1000) {
            return new ArrayList<>();
        }

        List<String> names = new ArrayList<>();
        String sql = "SELECT last_known_name FROM " + PLAYERDATA_TABLE_NAME + " WHERE last_known_name LIKE ? ORDER BY last_known_name ASC LIMIT ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, prefix + "%");
            pstmt.setInt(2, limit);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    names.add(rs.getString("last_known_name"));
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve player names starting with " + prefix, e);
        }
        return names;
    }

    public int importOfflinePlayers() {
        corePlugin.getLogger().info("[" + HomeModule.MODULE_NAME + "] Starting import of offline player data from server files...");
        OfflinePlayer[] offlinePlayers = Bukkit.getOfflinePlayers();
        int importedCount = 0;
        int currentBatch = 0;

        String sql = "INSERT INTO " + PLAYERDATA_TABLE_NAME + " (player_uuid, last_known_name, last_seen, first_seen) VALUES (?, ?, ?, ?) " +
                     "ON DUPLICATE KEY UPDATE last_known_name = VALUES(last_known_name), last_seen = IF(VALUES(last_seen) > last_seen, VALUES(last_seen), last_seen);";

        try (Connection conn = dbManager.getConnection()) {
            conn.setAutoCommit(false);

            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                for (OfflinePlayer player : offlinePlayers) {
                    if (player != null && player.getUniqueId() != null && player.getName() != null
                        && isValidUUID(player.getUniqueId()) && isValidPlayerName(player.getName())) {

                        pstmt.setString(1, player.getUniqueId().toString());
                        pstmt.setString(2, player.getName());

                        long lastSeenMillis = player.getLastSeen();
                        Timestamp lastSeenTs = (lastSeenMillis > 0) ? new Timestamp(lastSeenMillis) : new Timestamp(System.currentTimeMillis());
                        pstmt.setTimestamp(3, lastSeenTs);
                        pstmt.setTimestamp(4, lastSeenTs);

                        pstmt.addBatch();
                        currentBatch++;
                        importedCount++;

                        if (currentBatch >= batchSize) {
                            pstmt.executeBatch();
                            conn.commit();
                            currentBatch = 0;
                            corePlugin.getLogger().info("[" + HomeModule.MODULE_NAME + "] Imported " + importedCount + " players so far...");
                        }
                    }
                }

                if (currentBatch > 0) {
                    pstmt.executeBatch();
                    conn.commit();
                }

                corePlugin.getLogger().info("[" + HomeModule.MODULE_NAME + "] Offline player import completed. Total players processed: " + importedCount);
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Error during offline player import batch execution: ", e);
            try (Connection conn = dbManager.getConnection()) {
                conn.rollback();
            } catch (SQLException ex) {
                corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Error during rollback of player import: ", ex);
            }
        }
        return importedCount;
    }

    public List<String> getPlayerNamesWithHomes(@NotNull String prefix, int limit) {
        if (prefix.length() > 16 || limit <= 0 || limit > 1000) {
            return new ArrayList<>();
        }

        List<String> names = new ArrayList<>();
        String sql = "SELECT DISTINCT p.last_known_name " +
                     "FROM " + PLAYERDATA_TABLE_NAME + " p " +
                     "INNER JOIN " + HOMES_TABLE_NAME + " h ON p.player_uuid = h.player_uuid " +
                     "WHERE p.last_known_name LIKE ? " +
                     "ORDER BY p.last_known_name ASC " +
                     "LIMIT ?;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, prefix + "%");
            pstmt.setInt(2, limit);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    names.add(rs.getString("last_known_name"));
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve player names with homes starting with " + prefix, e);
        }
        return names;
    }

    @Nullable
    public UUID getPlayerUuidByName(@NotNull String playerName) {
        if (!isValidPlayerName(playerName)) {
            return null;
        }

        String sql = "SELECT player_uuid FROM " + PLAYERDATA_TABLE_NAME + " WHERE last_known_name = ? ORDER BY last_seen DESC LIMIT 1;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, playerName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return UUID.fromString(rs.getString("player_uuid"));
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not retrieve UUID for player name " + playerName, e);
        }
        return null;
    }

    public boolean isPlayerDataEmpty() {
        String sql = "SELECT COUNT(*) FROM " + PLAYERDATA_TABLE_NAME + " LIMIT 1;";
        try (Connection conn = dbManager.getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) == 0;
                }
            }
        } catch (SQLException e) {
            corePlugin.getLogger().log(Level.SEVERE, "[" + HomeModule.MODULE_NAME + "] Could not check if player data table is empty", e);
        }
        return false;
    }

    // --- Helper Methods ---

    private Home mapResultSetToHome(ResultSet rs) throws SQLException {
        return new Home(
                rs.getInt("id"),
                UUID.fromString(rs.getString("player_uuid")),
                rs.getString("home_name"),
                UUID.fromString(rs.getString("world_uuid")),
                rs.getDouble("x"), rs.getDouble("y"), rs.getDouble("z"),
                rs.getFloat("pitch"), rs.getFloat("yaw")
        );
    }

    private boolean isValidUUID(UUID uuid) {
        return uuid != null;
    }

    private boolean isValidHomeName(String homeName) {
        return homeName != null && !homeName.isEmpty() && homeName.length() <= 50 &&
               homeName.matches("^[a-zA-Z0-9_-]+$");
    }

    private boolean isValidPlayerName(String playerName) {
        return playerName != null && playerName.length() >= 3 && playerName.length() <= 16 &&
               playerName.matches("^[a-zA-Z0-9_]+$");
    }
}