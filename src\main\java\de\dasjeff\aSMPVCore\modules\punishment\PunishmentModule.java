package de.dasjeff.aSMPVCore.modules.punishment;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.IModule;
import de.dasjeff.aSMPVCore.modules.punishment.commands.HistoryCommand;
import de.dasjeff.aSMPVCore.modules.punishment.commands.PunishCommand;
import de.dasjeff.aSMPVCore.modules.punishment.commands.PunishAdminCommand;
import de.dasjeff.aSMPVCore.modules.punishment.config.PunishmentConfig;
import de.dasjeff.aSMPVCore.modules.punishment.config.MessageConfig;
import de.dasjeff.aSMPVCore.modules.punishment.database.PunishmentDataAccessor;
import de.dasjeff.aSMPVCore.modules.punishment.listeners.PlayerConnectionListener;
import de.dasjeff.aSMPVCore.modules.punishment.managers.PunishmentManager;
import de.dasjeff.aSMPVCore.modules.punishment.managers.VoiceChatManager;
import de.dasjeff.aSMPVCore.modules.punishment.service.PunishmentService;
import de.dasjeff.aSMPVCore.modules.punishment.util.PermissionUtil;

/**
 * Main punishment module for ASMP-VCore.
 * Handles all punishment-related functionality including bans, mutes, and voice mutes.
 *
 * Features:
 * - Template-based punishment system with escalating tiers
 * - Manual punishment options
 * - Voice chat integration via Simple Voice Chat API
 * - IP banning support
 * - Comprehensive punishment history
 * - Staff action logging
 * - Permission-based access control
 */
public class PunishmentModule implements IModule {

    public static final String MODULE_NAME = "PunishmentSystem";
    public static final String MODULE_VERSION = "1.0";
    public static final String MODULE_DESCRIPTION = "Advanced punishment system with template-based escalation and voice chat integration";

    // Core components
    private ASMPVCore corePlugin;
    private PunishmentConfig punishmentConfig;
    private MessageConfig messageConfig;
    private PunishmentDataAccessor dataAccessor;
    private PunishmentService punishmentService;
    private PunishmentManager punishmentManager;
    private VoiceChatManager voiceChatManager;
    private PermissionUtil permissionUtil;

    // Cache names
    public static final String ACTIVE_PUNISHMENTS_CACHE = "activePunishments";
    public static final String PUNISHMENT_HISTORY_CACHE = "punishmentHistory";
    public static final String TEMPLATE_TIERS_CACHE = "templateTiers";

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @Override
    public String getVersion() {
        return MODULE_VERSION;
    }

    @Override
    public String getDescription() {
        return MODULE_DESCRIPTION;
    }

    @Override
    public void onLoad(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        corePlugin.getLogger().info("[{}] Loading punishment module...", MODULE_NAME);

        try {
            // Initialize configurations
            this.punishmentConfig = new PunishmentConfig(corePlugin);
            this.messageConfig = new MessageConfig(corePlugin);

            // Initialize permission utility
            this.permissionUtil = new PermissionUtil();

            corePlugin.getLogger().info("[{}] Configurations loaded successfully.", MODULE_NAME);
        } catch (Exception e) {
            corePlugin.getLogger().error("[{}] Failed to load configurations!", MODULE_NAME, e);
            throw new RuntimeException("Failed to load punishment module configurations", e);
        }

        corePlugin.getLogger().info("[{}] Module loaded successfully.", MODULE_NAME);
    }

    @Override
    public void onEnable(ASMPVCore corePlugin) {
        corePlugin.getLogger().info("[{}] Enabling punishment module...", MODULE_NAME);

        if (this.corePlugin == null) {
            this.corePlugin = corePlugin;
        }

        // Validate configurations
        if (punishmentConfig == null || messageConfig == null) {
            corePlugin.getLogger().error("[{}] Cannot enable module due to configuration loading failure.", MODULE_NAME);
            return;
        }

        try {
            // Initialize database accessor
            this.dataAccessor = new PunishmentDataAccessor(corePlugin, this);
            corePlugin.getLogger().info("[{}] Database accessor initialized.", MODULE_NAME);

            // Initialize service layer
            this.punishmentService = new PunishmentService(corePlugin, this);
            corePlugin.getLogger().info("[{}] Service layer initialized.", MODULE_NAME);

            // Initialize managers
            this.punishmentManager = new PunishmentManager(corePlugin, this);
            this.voiceChatManager = new VoiceChatManager(corePlugin, this);
            corePlugin.getLogger().info("[{}] Managers initialized.", MODULE_NAME);

            // Initialize caches
            initializeCaches();

            // Register commands
            registerCommands();

            // Register listeners
            registerListeners();

            corePlugin.getLogger().info("[{}] Module enabled successfully!", MODULE_NAME);

        } catch (Exception e) {
            corePlugin.getLogger().error("[{}] Failed to enable module!", MODULE_NAME, e);
            throw new RuntimeException("Failed to enable punishment module", e);
        }
    }

    @Override
    public void onDisable() {
        corePlugin.getLogger().info("[{}] Disabling punishment module...", MODULE_NAME);

        try {
            // Shutdown voice chat manager
            if (voiceChatManager != null) {
                voiceChatManager.shutdown();
            }

            // Shutdown punishment manager
            if (punishmentManager != null) {
                punishmentManager.shutdown();
            }

            corePlugin.getLogger().info("[{}] Module disabled successfully.", MODULE_NAME);
        } catch (Exception e) {
            corePlugin.getLogger().error("[{}] Error during module shutdown!", MODULE_NAME, e);
        }
    }

    /**
     * Initializes caches for the punishment system.
     */
    private void initializeCaches() {
        if (corePlugin.getCacheManager() != null) {
            try {
                // Cache for active punishments (frequently accessed)
                corePlugin.getCacheManager().createLocalCache(
                    ACTIVE_PUNISHMENTS_CACHE,
                    1000,
                    15,
                    java.util.concurrent.TimeUnit.MINUTES
                );

                // Cache for punishment history (less frequently accessed)
                corePlugin.getCacheManager().createLocalCache(
                    PUNISHMENT_HISTORY_CACHE,
                    500,
                    30,
                    java.util.concurrent.TimeUnit.MINUTES
                );

                // Cache for template tier tracking
                corePlugin.getCacheManager().createLocalCache(
                    TEMPLATE_TIERS_CACHE,
                    2000,
                    60,
                    java.util.concurrent.TimeUnit.MINUTES
                );

                corePlugin.getLogger().info("[{}] Punishment caches initialized.", MODULE_NAME);
            } catch (IllegalArgumentException e) {
                corePlugin.getLogger().debug("[{}] Some caches already exist, continuing...", MODULE_NAME);
            }
        } else {
            corePlugin.getLogger().warn("[{}] CacheManager not available. Performance may be reduced.", MODULE_NAME);
        }
    }

    /**
     * Registers all punishment-related commands.
     */
    private void registerCommands() {
        try {
            PunishCommand punishCommand = new PunishCommand(corePlugin, this);
            HistoryCommand historyCommand = new HistoryCommand(corePlugin, this);
            PunishAdminCommand punishAdminCommand = new PunishAdminCommand(corePlugin, this);

            corePlugin.getCommandManager().registerCommand(punishCommand, "punish");
            corePlugin.getCommandManager().registerCommand(historyCommand, "history");
            corePlugin.getCommandManager().registerCommand(punishAdminCommand, "punishadmin");

            corePlugin.getLogger().info("[{}] Commands registered successfully.", MODULE_NAME);
        } catch (Exception e) {
            corePlugin.getLogger().error("[{}] Failed to register commands!", MODULE_NAME, e);
            throw new RuntimeException("Failed to register punishment commands", e);
        }
    }

    /**
     * Registers all punishment-related listeners.
     */
    private void registerListeners() {
        try {
            PlayerConnectionListener connectionListener = new PlayerConnectionListener(corePlugin, this);
            corePlugin.getListenerManager().registerListener(connectionListener);

            corePlugin.getLogger().info("[{}] Listeners registered successfully.", MODULE_NAME);
        } catch (Exception e) {
            corePlugin.getLogger().error("[{}] Failed to register listeners!", MODULE_NAME, e);
            throw new RuntimeException("Failed to register punishment listeners", e);
        }
    }

    // Getters for module components
    public ASMPVCore getCorePlugin() {
        return corePlugin;
    }

    public PunishmentConfig getPunishmentConfig() {
        return punishmentConfig;
    }

    public MessageConfig getMessageConfig() {
        return messageConfig;
    }

    public PunishmentDataAccessor getDataAccessor() {
        return dataAccessor;
    }

    public PunishmentService getPunishmentService() {
        return punishmentService;
    }

    public PunishmentManager getPunishmentManager() {
        return punishmentManager;
    }

    public VoiceChatManager getVoiceChatManager() {
        return voiceChatManager;
    }

    public PermissionUtil getPermissionUtil() {
        return permissionUtil;
    }
}
