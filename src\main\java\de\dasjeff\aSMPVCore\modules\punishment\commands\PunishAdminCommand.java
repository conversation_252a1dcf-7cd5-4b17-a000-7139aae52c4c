package de.dasjeff.aSMPVCore.modules.punishment.commands;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.commands.AbstractCommand;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Administrative command for punishment system management.
 * Usage: /punishadmin <reload|stats>
 */
public class PunishAdminCommand extends AbstractCommand {

    private final PunishmentModule punishmentModule;

    public PunishAdminCommand(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        super(corePlugin, "punishadmin", "asmp.vcore.punishment.config.reload", "Administrative punishment commands");
        this.punishmentModule = punishmentModule;
    }

    @Override
    protected void executeCommand(CommandSource source, String[] args) {
        if (args.length == 0) {
            sendUsage(source);
            return;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload" -> handleReload(source);
            case "stats" -> handleStats(source);
            default -> sendUsage(source);
        }
    }

    private void handleReload(CommandSource source) {
        // Check permission
        if (source instanceof Player player && !punishmentModule.getPermissionUtil().canReloadConfig(player)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "no_permission")
            ));
            return;
        }

        // Rate limiting for players
        if (source instanceof Player player) {
            if (!corePlugin.getSecurityManager().canExecuteCommand(player.getUniqueId())) {
                long remainingCooldown = corePlugin.getSecurityManager().getRemainingCooldown(player.getUniqueId());
                source.sendMessage(MessageUtil.prefixedError("Please wait " + remainingCooldown + "ms before using this command again."));
                return;
            }
        }

        CompletableFuture.runAsync(() -> {
            try {
                // Reload configurations
                punishmentModule.getPunishmentConfig().reload();
                punishmentModule.getMessageConfig().reload();

                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "config_reloaded")
                    ));
                }).schedule();

            } catch (Exception e) {
                corePlugin.getLogger().error("Error reloading punishment configuration", e);
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "reload_failed",
                            "{error}", e.getMessage())
                    ));
                }).schedule();
            }
        });
    }

    private void handleStats(CommandSource source) {
        // Check permission (same as reload for now)
        if (source instanceof Player player && !punishmentModule.getPermissionUtil().canReloadConfig(player)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "no_permission")
            ));
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                // TODO: Implement statistics gathering
                // For now, show basic template count
                int templateCount = punishmentModule.getPunishmentConfig().getTemplates().size();

                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "stats_header")
                    ));
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getCommandMessage("punishadmin", "stats_templates",
                            "{templates}", String.valueOf(templateCount))
                    ));
                    source.sendMessage(MessageUtil.prefixedInfo("Full statistics will be implemented in the next phase."));
                }).schedule();

            } catch (Exception e) {
                corePlugin.getLogger().error("Error gathering punishment statistics", e);
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getErrorMessage("internal_error")
                    ));
                }).schedule();
            }
        });
    }

    private void sendCommandUsage(CommandSource source) {
        source.sendMessage(MessageUtil.prefixedInfo("Usage: /punishadmin <reload|stats>"));
        source.sendMessage(MessageUtil.info("  reload - Reload punishment configuration"));
        source.sendMessage(MessageUtil.info("  stats  - Show punishment system statistics"));
    }

    @Override
    protected String getUsage() {
        return "/punishadmin <reload|stats>";
    }

    @Override
    protected List<String> getTabCompletions(CommandSource source, String[] args) {
        if (args.length == 1) {
            String partial = args[0].toLowerCase();
            return List.of("reload", "stats").stream()
                    .filter(cmd -> cmd.startsWith(partial))
                    .toList();
        }
        return List.of();
    }
}
