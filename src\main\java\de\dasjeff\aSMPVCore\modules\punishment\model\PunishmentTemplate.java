package de.dasjeff.aSMPVCore.modules.punishment.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Represents a punishment template with escalating tiers.
 * Templates define the structure and progression of punishments for specific offenses.
 */
public class PunishmentTemplate {
    
    private final String name;
    private final String displayName;
    private final PunishmentType type;
    private final String permissionNode;
    private final boolean ipBan;
    private final List<TemplateTier> tiers;

    public PunishmentTemplate(String name, String displayName, PunishmentType type, 
                             String permissionNode, boolean ipBan, List<TemplateTier> tiers) {
        this.name = Objects.requireNonNull(name, "Name cannot be null");
        this.displayName = Objects.requireNonNull(displayName, "Display name cannot be null");
        this.type = Objects.requireNonNull(type, "Type cannot be null");
        this.permissionNode = Objects.requireNonNull(permissionNode, "Permission node cannot be null");
        this.ipBan = ipBan;
        this.tiers = new ArrayList<>(Objects.requireNonNull(tiers, "Tiers cannot be null"));
        
        if (this.tiers.isEmpty()) {
            throw new IllegalArgumentException("Template must have at least one tier");
        }
        
        // Validate IP ban setting
        if (ipBan && !type.supportsIpBan()) {
            throw new IllegalArgumentException("IP ban is not supported for punishment type: " + type);
        }
    }

    /**
     * Gets the template name (internal identifier).
     */
    public String getName() {
        return name;
    }

    /**
     * Gets the display name shown to users.
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Gets the punishment type for this template.
     */
    public PunishmentType getType() {
        return type;
    }

    /**
     * Gets the permission node required to use this template.
     */
    public String getPermissionNode() {
        return permissionNode;
    }

    /**
     * Checks if this template should also apply IP bans.
     */
    public boolean isIpBan() {
        return ipBan;
    }

    /**
     * Gets all tiers for this template.
     */
    public List<TemplateTier> getTiers() {
        return new ArrayList<>(tiers);
    }

    /**
     * Gets the number of tiers in this template.
     */
    public int getTierCount() {
        return tiers.size();
    }

    /**
     * Gets a specific tier by ID.
     */
    public Optional<TemplateTier> getTier(int tierId) {
        return tiers.stream()
                   .filter(tier -> tier.getId() == tierId)
                   .findFirst();
    }

    /**
     * Gets the tier that should be applied based on the current tier count.
     * If the player has exceeded the maximum tiers, returns the last tier.
     */
    public TemplateTier getApplicableTier(int currentTierCount) {
        int tierIndex = Math.min(currentTierCount, tiers.size() - 1);
        return tiers.get(tierIndex);
    }

    /**
     * Gets the next tier that would be applied.
     */
    public Optional<TemplateTier> getNextTier(int currentTierCount) {
        int nextTierIndex = currentTierCount;
        if (nextTierIndex < tiers.size()) {
            return Optional.of(tiers.get(nextTierIndex));
        }
        return Optional.empty();
    }

    /**
     * Checks if this template has more tiers available.
     */
    public boolean hasMoreTiers(int currentTierCount) {
        return currentTierCount < tiers.size();
    }

    /**
     * Gets the maximum tier ID for this template.
     */
    public int getMaxTierId() {
        return tiers.stream()
                   .mapToInt(TemplateTier::getId)
                   .max()
                   .orElse(0);
    }

    /**
     * Gets a formatted display string for all tiers.
     */
    public String getTiersDisplayString() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < tiers.size(); i++) {
            if (i > 0) sb.append(", ");
            TemplateTier tier = tiers.get(i);
            sb.append(String.format("T%d: %s", tier.getId(), tier.getDuration()));
        }
        return sb.toString();
    }

    /**
     * Validates that this template is properly configured.
     */
    public boolean isValid() {
        try {
            // Check that all tiers have sequential IDs starting from 1
            for (int i = 0; i < tiers.size(); i++) {
                if (tiers.get(i).getId() != i + 1) {
                    return false;
                }
            }
            
            // Check that all required fields are present
            return name != null && !name.isEmpty() &&
                   displayName != null && !displayName.isEmpty() &&
                   type != null &&
                   permissionNode != null && !permissionNode.isEmpty() &&
                   !tiers.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PunishmentTemplate that = (PunishmentTemplate) o;
        return ipBan == that.ipBan &&
               Objects.equals(name, that.name) &&
               Objects.equals(displayName, that.displayName) &&
               type == that.type &&
               Objects.equals(permissionNode, that.permissionNode) &&
               Objects.equals(tiers, that.tiers);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, displayName, type, permissionNode, ipBan, tiers);
    }

    @Override
    public String toString() {
        return "PunishmentTemplate{" +
               "name='" + name + '\'' +
               ", displayName='" + displayName + '\'' +
               ", type=" + type +
               ", permissionNode='" + permissionNode + '\'' +
               ", ipBan=" + ipBan +
               ", tiers=" + tiers.size() +
               '}';
    }
}
