package de.dasjeff.aSMPVCore.model;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.Timestamp;
import java.util.UUID;

/**
 * Represents player data shared between Velocity and Paper cores.
 * This model corresponds to the 'players' table in the database.
 */
public class PlayerData {
    
    private final UUID playerUuid;
    private final String lastKnownName;
    private final Timestamp lastSeen;
    private final Timestamp firstSeen;
    private final int totalPunishments;
    private final Timestamp lastPunishmentDate;

    /**
     * Full constructor for database retrieval.
     */
    public PlayerData(@NotNull UUID playerUuid, 
                     @NotNull String lastKnownName, 
                     @NotNull Timestamp lastSeen,
                     @NotNull Timestamp firstSeen,
                     int totalPunishments,
                     @Nullable Timestamp lastPunishmentDate) {
        this.playerUuid = playerUuid;
        this.lastKnownName = lastKnownName;
        this.lastSeen = lastSeen;
        this.firstSeen = firstSeen;
        this.totalPunishments = totalPunishments;
        this.lastPunishmentDate = lastPunishmentDate;
    }

    /**
     * Constructor for new player creation (minimal data).
     */
    public PlayerData(@NotNull UUID playerUuid, @NotNull String lastKnownName) {
        this(playerUuid, lastKnownName, 
             new Timestamp(System.currentTimeMillis()), 
             new Timestamp(System.currentTimeMillis()),
             0, null);
    }

    /**
     * Constructor for legacy compatibility (without punishment data).
     */
    public PlayerData(@NotNull UUID playerUuid, 
                     @NotNull String lastKnownName, 
                     @NotNull Timestamp lastSeen) {
        this(playerUuid, lastKnownName, lastSeen, lastSeen, 0, null);
    }

    @NotNull
    public UUID getPlayerUuid() {
        return playerUuid;
    }

    @NotNull
    public String getLastKnownName() {
        return lastKnownName;
    }

    @NotNull
    public Timestamp getLastSeen() {
        return lastSeen;
    }

    @NotNull
    public Timestamp getFirstSeen() {
        return firstSeen;
    }

    public int getTotalPunishments() {
        return totalPunishments;
    }

    @Nullable
    public Timestamp getLastPunishmentDate() {
        return lastPunishmentDate;
    }

    /**
     * Checks if the player has any punishments.
     * @return true if the player has been punished before, false otherwise.
     */
    public boolean hasPunishments() {
        return totalPunishments > 0;
    }

    /**
     * Checks if the player was punished recently (within the last 30 days).
     * @return true if the player was punished in the last 30 days, false otherwise.
     */
    public boolean hasRecentPunishments() {
        if (lastPunishmentDate == null) {
            return false;
        }
        
        long thirtyDaysAgo = System.currentTimeMillis() - (30L * 24 * 60 * 60 * 1000);
        return lastPunishmentDate.getTime() > thirtyDaysAgo;
    }

    /**
     * Creates a new PlayerData instance with updated punishment statistics.
     * @param totalPunishments The new total punishment count.
     * @param lastPunishmentDate The timestamp of the most recent punishment.
     * @return A new PlayerData instance with updated punishment data.
     */
    public PlayerData withUpdatedPunishments(int totalPunishments, @Nullable Timestamp lastPunishmentDate) {
        return new PlayerData(playerUuid, lastKnownName, lastSeen, firstSeen, 
                             totalPunishments, lastPunishmentDate);
    }

    /**
     * Creates a new PlayerData instance with updated last seen timestamp.
     * @param lastSeen The new last seen timestamp.
     * @return A new PlayerData instance with updated last seen data.
     */
    public PlayerData withUpdatedLastSeen(@NotNull Timestamp lastSeen) {
        return new PlayerData(playerUuid, lastKnownName, lastSeen, firstSeen, 
                             totalPunishments, lastPunishmentDate);
    }

    /**
     * Creates a new PlayerData instance with updated name.
     * @param lastKnownName The new last known name.
     * @return A new PlayerData instance with updated name.
     */
    public PlayerData withUpdatedName(@NotNull String lastKnownName) {
        return new PlayerData(playerUuid, lastKnownName, lastSeen, firstSeen, 
                             totalPunishments, lastPunishmentDate);
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "playerUuid=" + playerUuid +
                ", lastKnownName='" + lastKnownName + '\'' +
                ", lastSeen=" + lastSeen +
                ", firstSeen=" + firstSeen +
                ", totalPunishments=" + totalPunishments +
                ", lastPunishmentDate=" + lastPunishmentDate +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PlayerData that = (PlayerData) o;
        return playerUuid.equals(that.playerUuid);
    }

    @Override
    public int hashCode() {
        return playerUuid.hashCode();
    }
}
