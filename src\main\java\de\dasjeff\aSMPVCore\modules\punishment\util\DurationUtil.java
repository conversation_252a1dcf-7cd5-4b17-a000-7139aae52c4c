package de.dasjeff.aSMPVCore.modules.punishment.util;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for parsing and handling duration strings.
 * Supports formats like "30m", "2h", "1d", "permanent", etc.
 */
public class DurationUtil {

    // Pattern for parsing duration strings (e.g., "30m", "2h", "1d")
    private static final Pattern DURATION_PATTERN = Pattern.compile("^(\\d+)([smhdwy])$", Pattern.CASE_INSENSITIVE);
    
    // Permanent duration keywords
    private static final String[] PERMANENT_KEYWORDS = {"permanent", "perm", "forever", "-1"};

    /**
     * Parses a duration string and returns the end time.
     * 
     * @param durationString The duration string to parse (e.g., "30m", "2h", "permanent")
     * @param startTime The start time to calculate from
     * @return The end time, or null if permanent
     * @throws IllegalArgumentException if the duration string is invalid
     */
    public static LocalDateTime parseEndTime(String durationString, LocalDateTime startTime) {
        if (durationString == null || durationString.trim().isEmpty()) {
            throw new IllegalArgumentException("Duration string cannot be null or empty");
        }

        String duration = durationString.trim().toLowerCase();

        // Check for permanent duration
        if (isPermanent(duration)) {
            return null;
        }

        // Parse duration with regex
        Matcher matcher = DURATION_PATTERN.matcher(duration);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid duration format: " + durationString + 
                ". Expected format: <number><unit> (e.g., 30m, 2h, 1d) or 'permanent'");
        }

        long amount = Long.parseLong(matcher.group(1));
        String unit = matcher.group(2).toLowerCase();

        if (amount <= 0) {
            throw new IllegalArgumentException("Duration amount must be positive: " + amount);
        }

        return switch (unit) {
            case "s" -> startTime.plus(amount, ChronoUnit.SECONDS);
            case "m" -> startTime.plus(amount, ChronoUnit.MINUTES);
            case "h" -> startTime.plus(amount, ChronoUnit.HOURS);
            case "d" -> startTime.plus(amount, ChronoUnit.DAYS);
            case "w" -> startTime.plus(amount * 7, ChronoUnit.DAYS);
            case "y" -> startTime.plus(amount * 365, ChronoUnit.DAYS);
            default -> throw new IllegalArgumentException("Invalid duration unit: " + unit + 
                ". Supported units: s, m, h, d, w, y");
        };
    }

    /**
     * Parses a duration string and returns the duration in milliseconds.
     * 
     * @param durationString The duration string to parse
     * @return The duration in milliseconds, or -1 if permanent
     * @throws IllegalArgumentException if the duration string is invalid
     */
    public static long parseDurationMillis(String durationString) {
        if (durationString == null || durationString.trim().isEmpty()) {
            throw new IllegalArgumentException("Duration string cannot be null or empty");
        }

        String duration = durationString.trim().toLowerCase();

        // Check for permanent duration
        if (isPermanent(duration)) {
            return -1;
        }

        // Parse duration with regex
        Matcher matcher = DURATION_PATTERN.matcher(duration);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid duration format: " + durationString);
        }

        long amount = Long.parseLong(matcher.group(1));
        String unit = matcher.group(2).toLowerCase();

        if (amount <= 0) {
            throw new IllegalArgumentException("Duration amount must be positive: " + amount);
        }

        return switch (unit) {
            case "s" -> amount * 1000L;
            case "m" -> amount * 60L * 1000L;
            case "h" -> amount * 60L * 60L * 1000L;
            case "d" -> amount * 24L * 60L * 60L * 1000L;
            case "w" -> amount * 7L * 24L * 60L * 60L * 1000L;
            case "y" -> amount * 365L * 24L * 60L * 60L * 1000L;
            default -> throw new IllegalArgumentException("Invalid duration unit: " + unit);
        };
    }

    /**
     * Checks if a duration string represents a permanent duration.
     */
    public static boolean isPermanent(String durationString) {
        if (durationString == null || durationString.trim().isEmpty()) {
            return false;
        }

        String duration = durationString.trim().toLowerCase();
        for (String keyword : PERMANENT_KEYWORDS) {
            if (keyword.equals(duration)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Formats a duration in milliseconds to a human-readable string.
     */
    public static String formatDuration(long durationMillis) {
        if (durationMillis < 0) {
            return "Permanent";
        }

        if (durationMillis == 0) {
            return "0s";
        }

        long seconds = durationMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        long weeks = days / 7;
        long years = days / 365;

        if (years > 0) {
            return years + "y" + (days % 365 > 0 ? " " + (days % 365) + "d" : "");
        } else if (weeks > 0) {
            return weeks + "w" + (days % 7 > 0 ? " " + (days % 7) + "d" : "");
        } else if (days > 0) {
            return days + "d" + (hours % 24 > 0 ? " " + (hours % 24) + "h" : "");
        } else if (hours > 0) {
            return hours + "h" + (minutes % 60 > 0 ? " " + (minutes % 60) + "m" : "");
        } else if (minutes > 0) {
            return minutes + "m" + (seconds % 60 > 0 ? " " + (seconds % 60) + "s" : "");
        } else {
            return seconds + "s";
        }
    }

    /**
     * Formats remaining time from now until the end time.
     */
    public static String formatRemainingTime(LocalDateTime endTime) {
        if (endTime == null) {
            return "Permanent";
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return "Expired";
        }

        long millis = java.time.Duration.between(now, endTime).toMillis();
        return formatDuration(millis);
    }

    /**
     * Validates if a duration string is in the correct format.
     */
    public static boolean isValidDuration(String durationString) {
        if (durationString == null || durationString.trim().isEmpty()) {
            return false;
        }

        String duration = durationString.trim().toLowerCase();

        // Check for permanent duration
        if (isPermanent(duration)) {
            return true;
        }

        // Check regex pattern
        Matcher matcher = DURATION_PATTERN.matcher(duration);
        if (!matcher.matches()) {
            return false;
        }

        try {
            long amount = Long.parseLong(matcher.group(1));
            return amount > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Gets example duration strings for help messages.
     */
    public static String[] getExampleDurations() {
        return new String[]{
            "30s", "5m", "2h", "1d", "1w", "permanent"
        };
    }

    /**
     * Gets a help string explaining duration format.
     */
    public static String getDurationHelp() {
        return "Duration format: <number><unit> where unit is s(econds), m(inutes), h(ours), d(ays), w(eeks), y(ears) or 'permanent'. " +
               "Examples: 30m, 2h, 1d, permanent";
    }

    /**
     * Converts a LocalDateTime to epoch milliseconds for database storage.
     */
    public static long toEpochMillis(LocalDateTime dateTime) {
        if (dateTime == null) {
            return -1;
        }
        return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * Converts epoch milliseconds to LocalDateTime.
     */
    public static LocalDateTime fromEpochMillis(long epochMillis) {
        if (epochMillis < 0) {
            return null;
        }
        return LocalDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(epochMillis),
            java.time.ZoneId.systemDefault()
        );
    }
}
