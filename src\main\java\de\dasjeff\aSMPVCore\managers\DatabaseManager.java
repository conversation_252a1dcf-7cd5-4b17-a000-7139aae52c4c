package de.dasjeff.aSMPVCore.managers;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.ConfigManager.ConfigWrapper;
import de.dasjeff.aSMPVCore.util.ConfigValidator;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Manages database connections using HikariCP connection pooling.
 * Provides thread-safe database access for the ASMP-VCore plugin.
 */
public class DatabaseManager {

    private final ASMPVCore corePlugin;
    private HikariDataSource dataSource;

    // Configuration values
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;
    private final int poolSize;
    private final long connectionTimeout;
    private final long idleTimeout;
    private final long maxLifetime;

    public DatabaseManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;

        ConfigWrapper config = corePlugin.getConfigManager().getMainConfig();

        // Load database configuration
        this.host = config.getString("database.host", "localhost");
        this.port = config.getInt("database.port", 3306);
        this.database = config.getString("database.database", "asmp_vcore");
        this.username = config.getString("database.username", "root");
        this.password = config.getString("database.password", "your_password");
        this.poolSize = config.getInt("database.pool_size", 10);
        this.connectionTimeout = config.getLong("database.connection_timeout", 30000);
        this.idleTimeout = config.getLong("database.idle_timeout", 600000);
        this.maxLifetime = config.getLong("database.max_lifetime", 1800000);
    }

    /**
     * Initializes the database connection pool.
     * @return true if initialization succeeded and config is valid; false to disable plugin functionality.
     */
    public boolean initialize() {
        try {
            // Validate database configuration
            ConfigValidator.validateDatabaseConfig(corePlugin.getConfigManager().getMainConfig());
        } catch (IllegalArgumentException e) {
            corePlugin.getLogger().error("==================================================");
            corePlugin.getLogger().error("ASMP-VCore DATABASE CONFIGURATION ERROR!");
            corePlugin.getLogger().error("Error: {}", e.getMessage());
            corePlugin.getLogger().error("Please fix your database configuration in config.yml");
            corePlugin.getLogger().error("==================================================");
            return false;
        }

        // Check for default configuration values
        boolean defaultConfig = host.equals("localhost")
                && port == 3306
                && database.equals("asmp_vcore")
                && username.equals("root")
                && password.equals("your_password");

        if (defaultConfig) {
            corePlugin.getLogger().error("==================================================");
            corePlugin.getLogger().error("ASMP-VCore DETECTED DEFAULT DATABASE CONFIGURATION!");
            corePlugin.getLogger().error("Please configure your database settings in config.yml");
            corePlugin.getLogger().error("Current settings:");
            corePlugin.getLogger().error("  Host: {}", host);
            corePlugin.getLogger().error("  Port: {}", port);
            corePlugin.getLogger().error("  Database: {}", database);
            corePlugin.getLogger().error("  Username: {}", username);
            corePlugin.getLogger().error("==================================================");
            return false;
        }

        return setupDataSource();
    }

    private boolean setupDataSource() {
        HikariConfig hikariConfig = new HikariConfig();

        // Basic connection settings
        hikariConfig.setJdbcUrl("jdbc:mariadb://" + host + ":" + port + "/" + database);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName("org.mariadb.jdbc.Driver");

        // Pool settings
        hikariConfig.setMaximumPoolSize(poolSize);
        hikariConfig.setMinimumIdle(Math.max(1, poolSize / 4));
        hikariConfig.setConnectionTimeout(connectionTimeout);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setMaxLifetime(maxLifetime);
        hikariConfig.setLeakDetectionThreshold(60000);

        // Connection properties for performance
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");
        hikariConfig.addDataSourceProperty("useLocalSessionState", "true");
        hikariConfig.addDataSourceProperty("rewriteBatchedStatements", "true");
        hikariConfig.addDataSourceProperty("cacheResultSetMetadata", "true");
        hikariConfig.addDataSourceProperty("cacheServerConfiguration", "true");
        hikariConfig.addDataSourceProperty("elideSetAutoCommits", "true");
        hikariConfig.addDataSourceProperty("maintainTimeStats", "false");

        // Connection validation
        hikariConfig.setConnectionTestQuery("SELECT 1");
        hikariConfig.setValidationTimeout(5000);

        // Pool name for monitoring
        hikariConfig.setPoolName("ASMP-VCore-Pool");

        try {
            this.dataSource = new HikariDataSource(hikariConfig);
            corePlugin.getLogger().info("Successfully established database connection pool to {}:{}/{}", host, port, database);

            // Test the connection
            try (Connection conn = getConnection()) {
                corePlugin.getLogger().info("Database connection test successful");
                return true;
            }
        } catch (Exception e) {
            corePlugin.getLogger().error("Could not establish database connection pool! Plugin functionality requiring database access will be disabled.", e);
            this.dataSource = null;
            return false;
        }
    }

    /**
     * Gets a connection from the pool.
     * @return A database connection.
     * @throws SQLException if no connection is available or database is not initialized.
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("Database source is not available. Check plugin logs for connection errors.");
        }
        return dataSource.getConnection();
    }

    /**
     * Checks if the database is connected and available.
     * @return true if connected, false otherwise.
     */
    public boolean isConnected() {
        return dataSource != null && !dataSource.isClosed();
    }

    /**
     * Gets the current pool statistics.
     * @return A string containing pool statistics, or null if not available.
     */
    public String getPoolStats() {
        if (dataSource == null) {
            return "Database not initialized";
        }

        return String.format("Pool Stats - Active: %d, Idle: %d, Total: %d, Waiting: %d",
                dataSource.getHikariPoolMXBean().getActiveConnections(),
                dataSource.getHikariPoolMXBean().getIdleConnections(),
                dataSource.getHikariPoolMXBean().getTotalConnections(),
                dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
    }

    /**
     * Closes the data source and all connections.
     * Should be called during plugin shutdown.
     */
    public void closeDataSource() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            corePlugin.getLogger().info("Database connection pool closed.");
        }
    }

    /**
     * Executes a database operation asynchronously.
     * @param operation The database operation to execute.
     */
    public void executeAsync(DatabaseOperation operation) {
        corePlugin.getProxyServer().getScheduler()
                .buildTask(corePlugin, () -> {
                    try (Connection conn = getConnection()) {
                        operation.execute(conn);
                    } catch (SQLException e) {
                        corePlugin.getLogger().error("Database operation failed", e);
                    }
                })
                .schedule();
    }

    /**
     * Executes a database operation in a transaction.
     * @param operation The database operation to execute.
     * @return true if the operation succeeded, false otherwise.
     */
    public boolean executeInTransaction(DatabaseOperation operation) {
        try (Connection conn = getConnection()) {
            conn.setAutoCommit(false);
            try {
                operation.execute(conn);
                conn.commit();
                return true;
            } catch (SQLException e) {
                conn.rollback();
                corePlugin.getLogger().error("Database transaction failed, rolled back", e);
                return false;
            }
        } catch (SQLException e) {
            corePlugin.getLogger().error("Database transaction setup failed", e);
            return false;
        }
    }

    /**
     * Initializes the core database schema for the punishment system.
     * This method handles migration from the old schema if needed.
     * @return true if initialization succeeded, false otherwise.
     */
    public boolean initializeCoreSchema() {
        if (!isConnected()) {
            corePlugin.getLogger().error("Database is not connected. Cannot initialize schema.");
            return false;
        }

        return executeInTransaction(conn -> {
            // 1. Check if old home_playerdata table exists and rename to players
            migratePlayerDataTable(conn);

            // 2. Create punishment tables
            createPunishmentTables(conn);

            corePlugin.getLogger().info("Core database schema initialized successfully.");
        });
    }

    /**
     * Migrates the old home_playerdata table to the new players table.
     */
    private void migratePlayerDataTable(Connection conn) throws SQLException {
        // Check if old table exists
        boolean oldTableExists = tableExists(conn, "home_playerdata");
        boolean newTableExists = tableExists(conn, "players");

        if (oldTableExists && !newTableExists) {
            // Rename old table to new name
            try (Statement stmt = conn.createStatement()) {
                stmt.executeUpdate("RENAME TABLE home_playerdata TO players");
                corePlugin.getLogger().info("Migrated table 'home_playerdata' to 'players'");
            }
        } else if (!oldTableExists && !newTableExists) {
            // Create new players table from scratch
            createPlayersTable(conn);
        }

        // Add new columns for punishment system if they don't exist
        addPunishmentColumnsToPlayers(conn);
    }

    /**
     * Creates the players table from scratch.
     */
    private void createPlayersTable(Connection conn) throws SQLException {
        String createPlayersTableSQL = """
            CREATE TABLE IF NOT EXISTS players (
                player_uuid CHAR(36) PRIMARY KEY,
                last_known_name VARCHAR(16) NOT NULL COLLATE utf8mb4_general_ci,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_punishments INT DEFAULT 0,
                last_punishment_date TIMESTAMP NULL,
                INDEX idx_last_known_name (last_known_name),
                INDEX idx_last_seen (last_seen),
                INDEX idx_total_punishments (total_punishments)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        try (Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createPlayersTableSQL);
            corePlugin.getLogger().info("Created 'players' table");
        }
    }

    /**
     * Adds punishment-related columns to the players table if they don't exist.
     */
    private void addPunishmentColumnsToPlayers(Connection conn) throws SQLException {
        if (!columnExists(conn, "players", "total_punishments")) {
            try (Statement stmt = conn.createStatement()) {
                stmt.executeUpdate("ALTER TABLE players ADD COLUMN total_punishments INT DEFAULT 0");
                stmt.executeUpdate("ALTER TABLE players ADD INDEX idx_total_punishments (total_punishments)");
                corePlugin.getLogger().info("Added 'total_punishments' column to players table");
            }
        }

        if (!columnExists(conn, "players", "last_punishment_date")) {
            try (Statement stmt = conn.createStatement()) {
                stmt.executeUpdate("ALTER TABLE players ADD COLUMN last_punishment_date TIMESTAMP NULL");
                corePlugin.getLogger().info("Added 'last_punishment_date' column to players table");
            }
        }
    }

    /**
     * Creates all punishment-related tables.
     */
    private void createPunishmentTables(Connection conn) throws SQLException {
        createPunishmentsTable(conn);
        createIpBansTable(conn);
        createStaffActionsLogTable(conn);
    }

    /**
     * Creates the main punishments table.
     */
    private void createPunishmentsTable(Connection conn) throws SQLException {
        String createPunishmentsTableSQL = """
            CREATE TABLE IF NOT EXISTS asmp_punishments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_uuid CHAR(36) NOT NULL,
                punishment_type ENUM('MUTE', 'VOICE_MUTE', 'BAN') NOT NULL,
                reason TEXT NOT NULL,
                staff_uuid CHAR(36) NOT NULL,
                staff_name VARCHAR(16) NOT NULL,
                internal_note TEXT NULL,
                template_name VARCHAR(255) NULL,
                template_stufe_id INT NULL,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP NULL,
                duration_string VARCHAR(50) NOT NULL,
                status ENUM('ACTIVE', 'EXPIRED', 'PARDONED') DEFAULT 'ACTIVE',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_player_uuid (player_uuid),
                INDEX idx_status (status),
                INDEX idx_type (punishment_type),
                INDEX idx_active_punishments (player_uuid, status, punishment_type),
                INDEX idx_template_tracking (player_uuid, template_name, status),
                FOREIGN KEY (player_uuid) REFERENCES players(player_uuid) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        try (Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createPunishmentsTableSQL);
            corePlugin.getLogger().info("Created 'asmp_punishments' table");
        }
    }

    /**
     * Creates the IP bans table.
     */
    private void createIpBansTable(Connection conn) throws SQLException {
        String createIpBansTableSQL = """
            CREATE TABLE IF NOT EXISTS asmp_ip_bans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ip_address VARCHAR(45) NOT NULL,
                reason TEXT NOT NULL,
                staff_uuid CHAR(36) NOT NULL,
                staff_name VARCHAR(16) NOT NULL,
                linked_player_uuid CHAR(36) NULL,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP NULL,
                duration_string VARCHAR(50) NOT NULL,
                status ENUM('ACTIVE', 'EXPIRED', 'PARDONED') DEFAULT 'ACTIVE',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_ip (ip_address),
                INDEX idx_status (status),
                INDEX idx_active_ip_bans (ip_address, status),
                FOREIGN KEY (linked_player_uuid) REFERENCES players(player_uuid) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        try (Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createIpBansTableSQL);
            corePlugin.getLogger().info("Created 'asmp_ip_bans' table");
        }
    }

    /**
     * Creates the staff actions log table.
     */
    private void createStaffActionsLogTable(Connection conn) throws SQLException {
        String createStaffActionsLogTableSQL = """
            CREATE TABLE IF NOT EXISTS asmp_staff_actions_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                staff_uuid CHAR(36) NOT NULL,
                staff_name VARCHAR(16) NOT NULL,
                action_type VARCHAR(255) NOT NULL,
                target_player_uuid CHAR(36) NULL,
                punishment_id INT NULL,
                details TEXT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_staff (staff_uuid),
                INDEX idx_timestamp (timestamp),
                INDEX idx_action_type (action_type),
                FOREIGN KEY (punishment_id) REFERENCES asmp_punishments(id) ON DELETE SET NULL,
                FOREIGN KEY (target_player_uuid) REFERENCES players(player_uuid) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """;

        try (Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(createStaffActionsLogTableSQL);
            corePlugin.getLogger().info("Created 'asmp_staff_actions_log' table");
        }
    }

    /**
     * Checks if a table exists in the database.
     */
    private boolean tableExists(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, database);
            pstmt.setString(2, tableName);
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        }
    }

    /**
     * Checks if a column exists in a table.
     */
    private boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = ? AND table_name = ? AND column_name = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, database);
            pstmt.setString(2, tableName);
            pstmt.setString(3, columnName);
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        }
    }

    /**
     * Functional interface for database operations.
     */
    @FunctionalInterface
    public interface DatabaseOperation {
        void execute(Connection connection) throws SQLException;
    }
}
