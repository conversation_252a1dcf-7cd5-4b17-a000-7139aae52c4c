package de.dasjeff.aSMPVCore.modules.punishment.model;

/**
 * Enumeration of punishment status values.
 */
public enum PunishmentStatus {
    /**
     * Punishment is currently active and being enforced.
     */
    ACTIVE("Active", "Currently active"),
    
    /**
     * Punishment has expired naturally.
     */
    EXPIRED("Expired", "Expired"),
    
    /**
     * Punishment was manually pardoned/revoked by staff.
     */
    PARDONED("Pardoned", "Manually pardoned");

    private final String displayName;
    private final String description;

    PunishmentStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Gets the punishment status from a string, case-insensitive.
     */
    public static PunishmentStatus fromString(String str) {
        if (str == null) return null;
        
        try {
            return valueOf(str.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Checks if this status represents an active punishment.
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * Checks if this status represents an inactive punishment.
     */
    public boolean isInactive() {
        return this == EXPIRED || this == PARDONED;
    }
}
