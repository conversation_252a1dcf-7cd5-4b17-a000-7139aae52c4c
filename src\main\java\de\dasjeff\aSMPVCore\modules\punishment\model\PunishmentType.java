package de.dasjeff.aSMPVCore.modules.punishment.model;

/**
 * Enumeration of available punishment types.
 */
public enum PunishmentType {
    /**
     * Chat mute - prevents player from sending chat messages.
     */
    MUTE("Mute", "Chat Mute"),
    
    /**
     * Voice mute - prevents player from using voice chat.
     */
    VOICE_MUTE("Voice Mute", "Voice Chat Mute"),
    
    /**
     * Ban - prevents player from joining the server.
     */
    BAN("Ban", "Server Ban");

    private final String displayName;
    private final String description;

    PunishmentType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Gets the punishment type from a string, case-insensitive.
     */
    public static PunishmentType fromString(String str) {
        if (str == null) return null;
        
        try {
            return valueOf(str.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Checks if this punishment type supports IP banning.
     */
    public boolean supportsIpBan() {
        return this == BAN;
    }

    /**
     * Checks if this punishment type requires the player to be online.
     */
    public boolean requiresOnlinePlayer() {
        return this == VOICE_MUTE;
    }
}
