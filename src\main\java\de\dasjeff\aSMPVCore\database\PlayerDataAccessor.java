package de.dasjeff.aSMPVCore.database;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.DatabaseManager;
import de.dasjeff.aSMPVCore.model.PlayerData;
import de.dasjeff.aSMPVCore.util.UUIDUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Data access layer for player data operations.
 * Handles all database interactions for the players table.
 */
public class PlayerDataAccessor {

    private final ASMPVCore corePlugin;
    private final DatabaseManager databaseManager;
    private final String PLAYERS_TABLE = "players";

    public PlayerDataAccessor(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.databaseManager = corePlugin.getDatabaseManager();
    }

    /**
     * Updates or creates player data in the database.
     * @param playerUuid The player's UUID.
     * @param playerName The player's current name.
     * @return CompletableFuture that completes when the operation is done.
     */
    public CompletableFuture<Boolean> updatePlayerData(@NotNull UUID playerUuid, @NotNull String playerName) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isValidUUID(playerUuid) || !isValidPlayerName(playerName)) {
                corePlugin.getLogger().warn("Invalid player data provided: UUID={}, Name={}", playerUuid, playerName);
                return false;
            }

            String sql = """
                INSERT INTO %s (player_uuid, last_known_name, last_seen, first_seen) 
                VALUES (?, ?, NOW(), NOW()) 
                ON DUPLICATE KEY UPDATE 
                    last_known_name = VALUES(last_known_name), 
                    last_seen = NOW()
                """.formatted(PLAYERS_TABLE);

            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, playerUuid.toString());
                pstmt.setString(2, playerName);
                
                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
                
            } catch (SQLException e) {
                corePlugin.getLogger().error("Failed to update player data for {}", playerName, e);
                return false;
            }
        });
    }

    /**
     * Retrieves player data by UUID.
     * @param playerUuid The player's UUID.
     * @return CompletableFuture containing the PlayerData or null if not found.
     */
    public CompletableFuture<PlayerData> getPlayerData(@NotNull UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isValidUUID(playerUuid)) {
                return null;
            }

            String sql = "SELECT * FROM " + PLAYERS_TABLE + " WHERE player_uuid = ?";
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, playerUuid.toString());
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        return mapResultSetToPlayerData(rs);
                    }
                }
                
            } catch (SQLException e) {
                corePlugin.getLogger().error("Failed to retrieve player data for UUID {}", playerUuid, e);
            }
            
            return null;
        });
    }

    /**
     * Retrieves player data by name (case-insensitive).
     * @param playerName The player's name.
     * @return CompletableFuture containing the PlayerData or null if not found.
     */
    public CompletableFuture<PlayerData> getPlayerDataByName(@NotNull String playerName) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isValidPlayerName(playerName)) {
                return null;
            }

            String sql = "SELECT * FROM " + PLAYERS_TABLE + " WHERE last_known_name = ? ORDER BY last_seen DESC LIMIT 1";
            
            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, playerName);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        return mapResultSetToPlayerData(rs);
                    }
                }
                
            } catch (SQLException e) {
                corePlugin.getLogger().error("Failed to retrieve player data for name {}", playerName, e);
            }
            
            return null;
        });
    }

    /**
     * Updates punishment statistics for a player.
     * @param playerUuid The player's UUID.
     * @param totalPunishments The new total punishment count.
     * @param lastPunishmentDate The timestamp of the most recent punishment.
     * @return CompletableFuture that completes when the operation is done.
     */
    public CompletableFuture<Boolean> updatePunishmentStats(@NotNull UUID playerUuid, 
                                                           int totalPunishments, 
                                                           @Nullable Timestamp lastPunishmentDate) {
        return CompletableFuture.supplyAsync(() -> {
            if (!isValidUUID(playerUuid)) {
                return false;
            }

            String sql = """
                UPDATE %s 
                SET total_punishments = ?, last_punishment_date = ? 
                WHERE player_uuid = ?
                """.formatted(PLAYERS_TABLE);

            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setInt(1, totalPunishments);
                pstmt.setTimestamp(2, lastPunishmentDate);
                pstmt.setString(3, playerUuid.toString());
                
                int affectedRows = pstmt.executeUpdate();
                return affectedRows > 0;
                
            } catch (SQLException e) {
                corePlugin.getLogger().error("Failed to update punishment stats for UUID {}", playerUuid, e);
                return false;
            }
        });
    }

    /**
     * Searches for players by name prefix.
     * @param namePrefix The name prefix to search for.
     * @param limit Maximum number of results to return.
     * @return CompletableFuture containing a list of matching player names.
     */
    public CompletableFuture<List<String>> searchPlayersByName(@NotNull String namePrefix, int limit) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> results = new ArrayList<>();
            
            if (namePrefix.length() < 2 || limit <= 0 || limit > 100) {
                return results;
            }

            String sql = """
                SELECT last_known_name FROM %s 
                WHERE last_known_name LIKE ? 
                ORDER BY last_seen DESC 
                LIMIT ?
                """.formatted(PLAYERS_TABLE);

            try (Connection conn = databaseManager.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {
                
                pstmt.setString(1, namePrefix + "%");
                pstmt.setInt(2, limit);
                
                try (ResultSet rs = pstmt.executeQuery()) {
                    while (rs.next()) {
                        results.add(rs.getString("last_known_name"));
                    }
                }
                
            } catch (SQLException e) {
                corePlugin.getLogger().error("Failed to search players by name prefix {}", namePrefix, e);
            }
            
            return results;
        });
    }

    /**
     * Maps a ResultSet row to a PlayerData object.
     */
    private PlayerData mapResultSetToPlayerData(ResultSet rs) throws SQLException {
        return new PlayerData(
            UUID.fromString(rs.getString("player_uuid")),
            rs.getString("last_known_name"),
            rs.getTimestamp("last_seen"),
            rs.getTimestamp("first_seen"),
            rs.getInt("total_punishments"),
            rs.getTimestamp("last_punishment_date")
        );
    }

    /**
     * Validates a UUID.
     */
    private boolean isValidUUID(UUID uuid) {
        return uuid != null && !UUIDUtil.isNilUUID(uuid);
    }

    /**
     * Validates a player name.
     */
    private boolean isValidPlayerName(String playerName) {
        return playerName != null && 
               playerName.length() >= 3 && 
               playerName.length() <= 16 && 
               playerName.matches("^[a-zA-Z0-9_]+$");
    }
}
