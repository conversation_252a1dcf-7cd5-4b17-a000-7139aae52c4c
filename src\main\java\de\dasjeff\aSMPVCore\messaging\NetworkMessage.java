package de.dasjeff.aSMPVCore.messaging;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Represents a message sent between servers in the network.
 * All cross-server communication uses this standardized message format.
 */
public class NetworkMessage {
    
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    
    private final String messageId;
    private final MessageType type;
    private final String sourceServer;
    private final long timestamp;
    private final Map<String, Object> data;
    private final int priority;
    
    /**
     * Creates a new network message.
     * @param type The message type.
     * @param sourceServer The server that sent this message.
     * @param data The message data payload.
     */
    public NetworkMessage(@NotNull MessageType type, @NotNull String sourceServer, @NotNull Map<String, Object> data) {
        this.messageId = UUID.randomUUID().toString();
        this.type = type;
        this.sourceServer = sourceServer;
        this.timestamp = System.currentTimeMillis();
        this.data = new HashMap<>(data);
        this.priority = type.getPriority();
    }
    
    /**
     * Private constructor for deserialization.
     */
    private NetworkMessage(String messageId, MessageType type, String sourceServer, 
                          long timestamp, Map<String, Object> data, int priority) {
        this.messageId = messageId;
        this.type = type;
        this.sourceServer = sourceServer;
        this.timestamp = timestamp;
        this.data = data;
        this.priority = priority;
    }
    
    /**
     * Gets the unique message ID.
     * @return The message ID.
     */
    @NotNull
    public String getMessageId() {
        return messageId;
    }
    
    /**
     * Gets the message type.
     * @return The message type.
     */
    @NotNull
    public MessageType getType() {
        return type;
    }
    
    /**
     * Gets the source server name.
     * @return The server that sent this message.
     */
    @NotNull
    public String getSourceServer() {
        return sourceServer;
    }
    
    /**
     * Gets the message timestamp.
     * @return The timestamp when this message was created.
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Gets the message data payload.
     * @return A copy of the message data.
     */
    @NotNull
    public Map<String, Object> getData() {
        return new HashMap<>(data);
    }
    
    /**
     * Gets the message priority.
     * @return The priority level (1-5, where 5 is highest).
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * Gets a data value as a string.
     * @param key The data key.
     * @return The string value, or null if not found or not a string.
     */
    @Nullable
    public String getString(@NotNull String key) {
        Object value = data.get(key);
        return value instanceof String ? (String) value : null;
    }
    
    /**
     * Gets a data value as an integer.
     * @param key The data key.
     * @return The integer value, or null if not found or not a number.
     */
    @Nullable
    public Integer getInt(@NotNull String key) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return null;
    }
    
    /**
     * Gets a data value as a long.
     * @param key The data key.
     * @return The long value, or null if not found or not a number.
     */
    @Nullable
    public Long getLong(@NotNull String key) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return null;
    }
    
    /**
     * Gets a data value as a boolean.
     * @param key The data key.
     * @return The boolean value, or null if not found or not a boolean.
     */
    @Nullable
    public Boolean getBoolean(@NotNull String key) {
        Object value = data.get(key);
        return value instanceof Boolean ? (Boolean) value : null;
    }
    
    /**
     * Gets a data value as a UUID.
     * @param key The data key.
     * @return The UUID value, or null if not found or not a valid UUID string.
     */
    @Nullable
    public UUID getUUID(@NotNull String key) {
        String value = getString(key);
        if (value != null) {
            try {
                return UUID.fromString(value);
            } catch (IllegalArgumentException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * Checks if the message contains a specific data key.
     * @param key The data key to check.
     * @return true if the key exists, false otherwise.
     */
    public boolean hasData(@NotNull String key) {
        return data.containsKey(key);
    }
    
    /**
     * Gets the age of this message in milliseconds.
     * @return The age in milliseconds.
     */
    public long getAge() {
        return System.currentTimeMillis() - timestamp;
    }
    
    /**
     * Checks if this message is older than the specified age.
     * @param maxAgeMs The maximum age in milliseconds.
     * @return true if the message is too old, false otherwise.
     */
    public boolean isExpired(long maxAgeMs) {
        return getAge() > maxAgeMs;
    }
    
    /**
     * Serializes this message to JSON.
     * @return The JSON representation of this message.
     */
    @NotNull
    public String toJson() {
        return GSON.toJson(this);
    }
    
    /**
     * Deserializes a message from JSON.
     * @param json The JSON string.
     * @return The deserialized message, or null if parsing failed.
     */
    @Nullable
    public static NetworkMessage fromJson(@NotNull String json) {
        try {
            return GSON.fromJson(json, NetworkMessage.class);
        } catch (JsonSyntaxException e) {
            return null;
        }
    }
    
    /**
     * Creates a builder for constructing network messages.
     * @param type The message type.
     * @param sourceServer The source server name.
     * @return A new message builder.
     */
    @NotNull
    public static Builder builder(@NotNull MessageType type, @NotNull String sourceServer) {
        return new Builder(type, sourceServer);
    }
    
    /**
     * Builder class for creating network messages with a fluent API.
     */
    public static class Builder {
        private final MessageType type;
        private final String sourceServer;
        private final Map<String, Object> data = new HashMap<>();
        
        private Builder(MessageType type, String sourceServer) {
            this.type = type;
            this.sourceServer = sourceServer;
        }
        
        /**
         * Adds a string data value.
         * @param key The data key.
         * @param value The string value.
         * @return This builder for chaining.
         */
        @NotNull
        public Builder data(@NotNull String key, @Nullable String value) {
            if (value != null) {
                data.put(key, value);
            }
            return this;
        }
        
        /**
         * Adds an integer data value.
         * @param key The data key.
         * @param value The integer value.
         * @return This builder for chaining.
         */
        @NotNull
        public Builder data(@NotNull String key, int value) {
            data.put(key, value);
            return this;
        }
        
        /**
         * Adds a long data value.
         * @param key The data key.
         * @param value The long value.
         * @return This builder for chaining.
         */
        @NotNull
        public Builder data(@NotNull String key, long value) {
            data.put(key, value);
            return this;
        }
        
        /**
         * Adds a boolean data value.
         * @param key The data key.
         * @param value The boolean value.
         * @return This builder for chaining.
         */
        @NotNull
        public Builder data(@NotNull String key, boolean value) {
            data.put(key, value);
            return this;
        }
        
        /**
         * Adds a UUID data value.
         * @param key The data key.
         * @param value The UUID value.
         * @return This builder for chaining.
         */
        @NotNull
        public Builder data(@NotNull String key, @Nullable UUID value) {
            if (value != null) {
                data.put(key, value.toString());
            }
            return this;
        }
        
        /**
         * Builds the network message.
         * @return The constructed network message.
         */
        @NotNull
        public NetworkMessage build() {
            return new NetworkMessage(type, sourceServer, data);
        }
    }
    
    @Override
    public String toString() {
        return "NetworkMessage{" +
                "messageId='" + messageId + '\'' +
                ", type=" + type +
                ", sourceServer='" + sourceServer + '\'' +
                ", timestamp=" + timestamp +
                ", priority=" + priority +
                ", dataKeys=" + data.keySet() +
                '}';
    }
}
