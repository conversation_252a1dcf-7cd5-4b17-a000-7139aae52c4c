package de.dasjeff.aSMPVCore.modules.punishment.commands;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.commands.AbstractCommand;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Command for viewing punishment history.
 * Usage: /history [player]
 */
public class HistoryCommand extends AbstractCommand {

    private final PunishmentModule punishmentModule;

    public HistoryCommand(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        super(corePlugin, "history", "asmp.vcore.punishment.access", "View punishment history");
        this.punishmentModule = punishmentModule;
    }

    @Override
    protected void executeCommand(CommandSource source, String[] args) {
        // Only players can use this command (GUI-based)
        if (!(source instanceof Player player)) {
            source.sendMessage(MessageUtil.prefixedError("This command can only be used by players."));
            return;
        }

        // Check basic permission
        if (!punishmentModule.getPermissionUtil().hasAccess(player)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getCommandMessage("history", "no_permission")
            ));
            return;
        }

        String targetPlayerName;

        if (args.length == 0) {
            // View own history
            targetPlayerName = player.getUsername();
        } else if (args.length == 1) {
            // View another player's history
            targetPlayerName = args[0];

            // Check permission to view others' history
            if (!punishmentModule.getPermissionUtil().canViewOthersHistory(player)) {
                source.sendMessage(MessageUtil.formatLegacy(
                    punishmentModule.getMessageConfig().getCommandMessage("history", "no_permission_others")
                ));
                return;
            }
        } else {
            source.sendMessage(MessageUtil.prefixedError("Usage: /history [player]"));
            return;
        }

        // Security check
        if (!corePlugin.getSecurityManager().isInputSafe(targetPlayerName)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getErrorMessage("invalid_input", "{input}", targetPlayerName)
            ));
            return;
        }

        // Rate limiting
        if (!corePlugin.getSecurityManager().canExecuteCommand(player.getUniqueId())) {
            long remainingCooldown = corePlugin.getSecurityManager().getRemainingCooldown(player.getUniqueId());
            source.sendMessage(MessageUtil.prefixedError("Please wait " + remainingCooldown + "ms before using this command again."));
            return;
        }

        // Execute asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                // TODO: Implement history lookup and GUI opening
                // For now, just send a placeholder message
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getCommandMessage("history", "loading",
                            "{player}", targetPlayerName)
                    ));
                    source.sendMessage(MessageUtil.prefixedInfo("History GUI system will be implemented in the next phase."));
                }).schedule();

            } catch (Exception e) {
                corePlugin.getLogger().error("Error executing history command", e);
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getErrorMessage("internal_error")
                    ));
                }).schedule();
            }
        });
    }

    @Override
    protected String getUsage() {
        return "/history [player]";
    }

    @Override
    protected List<String> getTabCompletions(CommandSource source, String[] args) {
        if (args.length == 1 && source instanceof Player player) {
            // Only suggest if player has permission to view others' history
            if (punishmentModule.getPermissionUtil().canViewOthersHistory(player)) {
                String partial = args[0].toLowerCase();
                return corePlugin.getProxyServer().getAllPlayers().stream()
                        .map(Player::getUsername)
                        .filter(name -> name.toLowerCase().startsWith(partial))
                        .sorted()
                        .toList();
            }
        }
        return List.of();
    }
}
