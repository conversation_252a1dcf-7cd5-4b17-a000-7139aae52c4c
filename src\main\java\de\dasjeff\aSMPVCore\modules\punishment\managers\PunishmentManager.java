package de.dasjeff.aSMPVCore.modules.punishment.managers;

import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.modules.punishment.model.Punishment;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Manager for applying and enforcing punishments.
 * Handles bans, mutes, and coordination with other servers.
 */
public class PunishmentManager {

    private final ASMPVCore corePlugin;
    private final PunishmentModule punishmentModule;

    public PunishmentManager(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        this.corePlugin = corePlugin;
        this.punishmentModule = punishmentModule;
    }

    /**
     * Applies a ban punishment to a player.
     */
    public boolean applyBan(Punishment punishment) {
        try {
            // Find the player if online
            Optional<Player> playerOpt = corePlugin.getProxyServer().getPlayer(punishment.getPlayerUuid());
            
            if (playerOpt.isPresent()) {
                Player player = playerOpt.get();
                
                // Create ban message
                String banMessage = createBanMessage(punishment);
                
                // Disconnect the player
                player.disconnect(MessageUtil.formatLegacy(banMessage));
                
                corePlugin.getLogger().info("Banned player {} ({}): {}", 
                    player.getUsername(), punishment.getPlayerUuid(), punishment.getReason());
            } else {
                corePlugin.getLogger().info("Applied ban to offline player {}: {}", 
                    punishment.getPlayerUuid(), punishment.getReason());
            }

            // Send cross-server notification
            sendCrossServerNotification(punishment);

            // TODO: Handle IP ban if enabled
            if (punishment.isFromTemplate()) {
                var template = punishmentModule.getPunishmentConfig().getTemplate(punishment.getTemplateName());
                if (template != null && template.isIpBan()) {
                    // TODO: Implement IP ban logic
                    corePlugin.getLogger().info("IP ban would be applied for punishment {}", punishment.getId());
                }
            }

            return true;

        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to apply ban punishment", e);
            return false;
        }
    }

    /**
     * Applies a mute punishment to a player.
     */
    public boolean applyMute(Punishment punishment) {
        try {
            // Find the player if online
            Optional<Player> playerOpt = corePlugin.getProxyServer().getPlayer(punishment.getPlayerUuid());
            
            if (playerOpt.isPresent()) {
                Player player = playerOpt.get();
                
                // Create mute message
                String muteMessage = createMuteMessage(punishment);
                
                // Send notification to player
                player.sendMessage(MessageUtil.formatLegacy(muteMessage));
                
                corePlugin.getLogger().info("Muted player {} ({}): {}", 
                    player.getUsername(), punishment.getPlayerUuid(), punishment.getReason());
            } else {
                corePlugin.getLogger().info("Applied mute to offline player {}: {}", 
                    punishment.getPlayerUuid(), punishment.getReason());
            }

            // Send cross-server notification
            sendCrossServerNotification(punishment);

            return true;

        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to apply mute punishment", e);
            return false;
        }
    }

    /**
     * Checks if a player is currently banned.
     */
    public boolean isBanned(java.util.UUID playerUuid) {
        try {
            Optional<Punishment> ban = punishmentModule.getDataAccessor().getActiveBan(playerUuid);
            if (ban.isPresent()) {
                Punishment punishment = ban.get();
                
                // Check if ban has expired
                if (punishment.isExpired()) {
                    // Mark as expired
                    punishmentModule.getDataAccessor().updatePunishmentStatus(
                        punishment.getId(), 
                        de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus.EXPIRED
                    );
                    return false;
                }
                
                return true;
            }
            return false;
        } catch (Exception e) {
            corePlugin.getLogger().error("Error checking ban status", e);
            return false;
        }
    }

    /**
     * Checks if a player is currently muted.
     */
    public boolean isMuted(java.util.UUID playerUuid) {
        try {
            Optional<Punishment> mute = punishmentModule.getDataAccessor().getActiveMute(playerUuid);
            if (mute.isPresent()) {
                Punishment punishment = mute.get();
                
                // Check if mute has expired
                if (punishment.isExpired()) {
                    // Mark as expired
                    punishmentModule.getDataAccessor().updatePunishmentStatus(
                        punishment.getId(), 
                        de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus.EXPIRED
                    );
                    return false;
                }
                
                return true;
            }
            return false;
        } catch (Exception e) {
            corePlugin.getLogger().error("Error checking mute status", e);
            return false;
        }
    }

    /**
     * Gets the active ban for a player, if any.
     */
    public Optional<Punishment> getActiveBan(java.util.UUID playerUuid) {
        return punishmentModule.getDataAccessor().getActiveBan(playerUuid);
    }

    /**
     * Gets the active mute for a player, if any.
     */
    public Optional<Punishment> getActiveMute(java.util.UUID playerUuid) {
        return punishmentModule.getDataAccessor().getActiveMute(playerUuid);
    }

    /**
     * Creates a ban message for the player.
     */
    private String createBanMessage(Punishment punishment) {
        if (punishment.isPermanent()) {
            return punishmentModule.getMessageConfig().getPlayerNotification("permanent_ban",
                "{reason}", punishment.getReason());
        } else {
            return punishmentModule.getMessageConfig().getPlayerNotification("temporary_ban",
                "{reason}", punishment.getReason(),
                "{duration}", punishment.getFormattedDuration());
        }
    }

    /**
     * Creates a mute message for the player.
     */
    private String createMuteMessage(Punishment punishment) {
        return punishmentModule.getMessageConfig().getPlayerNotification("mute",
            "{reason}", punishment.getReason(),
            "{duration}", punishment.getFormattedDuration());
    }

    /**
     * Sends cross-server notification about the punishment.
     */
    private void sendCrossServerNotification(Punishment punishment) {
        CompletableFuture.runAsync(() -> {
            try {
                // TODO: Implement cross-server messaging
                // This would notify all Paper servers about the punishment
                corePlugin.getLogger().debug("Cross-server notification sent for punishment {}", punishment.getId());
            } catch (Exception e) {
                corePlugin.getLogger().error("Failed to send cross-server notification", e);
            }
        });
    }

    /**
     * Shuts down the punishment manager.
     */
    public void shutdown() {
        corePlugin.getLogger().info("PunishmentManager shutting down...");
        // TODO: Cleanup any resources if needed
    }
}
