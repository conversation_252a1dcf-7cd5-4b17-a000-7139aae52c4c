package de.dasjeff.aSMPVCore.modules.punishment.service;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.modules.punishment.model.Punishment;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentTemplate;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentType;
import de.dasjeff.aSMPVCore.modules.punishment.model.TemplateTier;
import de.dasjeff.aSMPVCore.modules.punishment.util.DurationUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service layer for punishment operations.
 * Handles business logic and coordinates between different components.
 */
public class PunishmentService {

    private final ASMPVCore corePlugin;
    private final PunishmentModule punishmentModule;

    public PunishmentService(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        this.corePlugin = corePlugin;
        this.punishmentModule = punishmentModule;
    }

    /**
     * Applies a template-based punishment to a player.
     */
    public PunishmentResult applyTemplatePunishment(UUID playerUuid, String playerName,
                                                   UUID staffUuid, String staffName,
                                                   String templateName, String internalNote) {
        try {
            // Get the template
            PunishmentTemplate template = punishmentModule.getPunishmentConfig().getTemplate(templateName);
            if (template == null) {
                return PunishmentResult.failure("Template not found: " + templateName);
            }

            // Check if player already has an active punishment of this type
            Optional<Punishment> existingPunishment = getActivePunishment(playerUuid, template.getType());
            if (existingPunishment.isPresent()) {
                return PunishmentResult.failure("Player already has an active " + template.getType().getDisplayName());
            }

            // Get current tier count for this template
            int currentTierCount = punishmentModule.getDataAccessor().getTemplateTierCount(playerUuid, templateName);

            // Get the applicable tier
            TemplateTier tier = template.getApplicableTier(currentTierCount);

            // Calculate end time
            LocalDateTime startTime = LocalDateTime.now();
            LocalDateTime endTime = DurationUtil.parseEndTime(tier.getDuration(), startTime);

            // Create punishment record
            Punishment punishment = new Punishment(
                playerUuid,
                template.getType(),
                tier.getReasonTemplate(),
                staffUuid,
                staffName,
                internalNote,
                templateName,
                tier.getId(),
                startTime,
                endTime,
                tier.getDuration()
            );

            // Save to database
            boolean success = punishmentModule.getDataAccessor().createPunishment(punishment);
            if (!success) {
                return PunishmentResult.failure("Failed to save punishment to database");
            }

            // Apply the punishment (ban, mute, etc.)
            boolean applied = applyPunishmentEffect(punishment);
            if (!applied) {
                return PunishmentResult.failure("Failed to apply punishment effect");
            }

            // Clear relevant caches
            clearPlayerCaches(playerUuid);

            corePlugin.getLogger().info("Applied template punishment: {} to {} by {} (Template: {}, Tier: {})",
                template.getType(), playerName, staffName, templateName, tier.getId());

            return PunishmentResult.success(punishment);

        } catch (Exception e) {
            corePlugin.getLogger().error("Error applying template punishment", e);
            return PunishmentResult.failure("Internal error: " + e.getMessage());
        }
    }

    /**
     * Applies a manual punishment to a player.
     */
    public PunishmentResult applyManualPunishment(UUID playerUuid, String playerName,
                                                 UUID staffUuid, String staffName,
                                                 PunishmentType type, String reason,
                                                 String duration, String internalNote) {
        try {
            // Check if player already has an active punishment of this type
            Optional<Punishment> existingPunishment = getActivePunishment(playerUuid, type);
            if (existingPunishment.isPresent()) {
                return PunishmentResult.failure("Player already has an active " + type.getDisplayName());
            }

            // Calculate end time
            LocalDateTime startTime = LocalDateTime.now();
            LocalDateTime endTime = DurationUtil.parseEndTime(duration, startTime);

            // Create punishment record
            Punishment punishment = new Punishment(
                playerUuid,
                type,
                reason,
                staffUuid,
                staffName,
                internalNote,
                null, // No template for manual punishments
                null, // No tier for manual punishments
                startTime,
                endTime,
                duration
            );

            // Save to database
            boolean success = punishmentModule.getDataAccessor().createPunishment(punishment);
            if (!success) {
                return PunishmentResult.failure("Failed to save punishment to database");
            }

            // Apply the punishment effect
            boolean applied = applyPunishmentEffect(punishment);
            if (!applied) {
                return PunishmentResult.failure("Failed to apply punishment effect");
            }

            // Clear relevant caches
            clearPlayerCaches(playerUuid);

            corePlugin.getLogger().info("Applied manual punishment: {} to {} by {} (Duration: {})",
                type, playerName, staffName, duration);

            return PunishmentResult.success(punishment);

        } catch (Exception e) {
            corePlugin.getLogger().error("Error applying manual punishment", e);
            return PunishmentResult.failure("Internal error: " + e.getMessage());
        }
    }

    /**
     * Gets the active punishment of a specific type for a player.
     */
    public Optional<Punishment> getActivePunishment(UUID playerUuid, PunishmentType type) {
        return switch (type) {
            case BAN -> punishmentModule.getDataAccessor().getActiveBan(playerUuid);
            case MUTE -> punishmentModule.getDataAccessor().getActiveMute(playerUuid);
            case VOICE_MUTE -> punishmentModule.getDataAccessor().getActiveVoiceMute(playerUuid);
        };
    }

    /**
     * Gets the punishment history for a player.
     */
    public List<Punishment> getPunishmentHistory(UUID playerUuid) {
        return punishmentModule.getDataAccessor().getPunishmentHistory(playerUuid);
    }

    /**
     * Gets the next tier that would be applied for a template.
     */
    public Optional<TemplateTier> getNextTemplateTier(UUID playerUuid, String templateName) {
        PunishmentTemplate template = punishmentModule.getPunishmentConfig().getTemplate(templateName);
        if (template == null) {
            return Optional.empty();
        }

        int currentTierCount = punishmentModule.getDataAccessor().getTemplateTierCount(playerUuid, templateName);
        return template.getNextTier(currentTierCount);
    }

    /**
     * Applies the actual punishment effect (ban, mute, etc.).
     */
    private boolean applyPunishmentEffect(Punishment punishment) {
        return switch (punishment.getType()) {
            case BAN -> punishmentModule.getPunishmentManager().applyBan(punishment);
            case MUTE -> punishmentModule.getPunishmentManager().applyMute(punishment);
            case VOICE_MUTE -> punishmentModule.getVoiceChatManager().applyVoiceMute(punishment);
        };
    }

    /**
     * Clears cached data for a player.
     */
    private void clearPlayerCaches(UUID playerUuid) {
        if (corePlugin.getCacheManager() != null) {
            String playerKey = playerUuid.toString();
            corePlugin.getCacheManager().invalidateLocal(PunishmentModule.ACTIVE_PUNISHMENTS_CACHE, playerKey);
            corePlugin.getCacheManager().invalidateLocal(PunishmentModule.PUNISHMENT_HISTORY_CACHE, playerKey);
            corePlugin.getCacheManager().invalidateLocal(PunishmentModule.TEMPLATE_TIERS_CACHE, playerKey);
        }
    }

    /**
     * Result class for punishment operations.
     */
    public static class PunishmentResult {
        private final boolean success;
        private final String message;
        private final Punishment punishment;

        private PunishmentResult(boolean success, String message, Punishment punishment) {
            this.success = success;
            this.message = message;
            this.punishment = punishment;
        }

        public static PunishmentResult success(Punishment punishment) {
            return new PunishmentResult(true, "Success", punishment);
        }

        public static PunishmentResult failure(String message) {
            return new PunishmentResult(false, message, null);
        }

        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Punishment getPunishment() { return punishment; }
    }
}
