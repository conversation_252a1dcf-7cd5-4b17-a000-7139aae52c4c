# ===================================================================
#                    ASMP-VCore Configuration
# ===================================================================
# Main configuration file for the ASMP-VCore plugin.
# This plugin manages network-wide communication between proxy,
# paper servers, and webpanel.

# ===================================================================
#                       SERVER SETTINGS
# ===================================================================
# Server identification for messaging system
server:
  name: "velocity-proxy"
  instance_id: "proxy-01"

# ===================================================================
#                       DATABASE SETTINGS
# ===================================================================
# Database configuration for MySQL/MariaDB
database:
  enabled: true
  host: "localhost"
  port: 3306
  database: "adventuresmp_core"
  username: "root"
  password: "your_password"

  # Connection pool settings
  pool_size: 10
  connection_timeout: 30000
  idle_timeout: 600000
  max_lifetime: 1800000

# ===================================================================
#                       REDIS SETTINGS
# ===================================================================
# Redis configuration for distributed caching and messaging
redis:
  enabled: true
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  timeout: 2000

  # Connection pool settings
  pool_max_total: 20
  pool_max_idle: 10
  pool_min_idle: 2

# ===================================================================
#                       CACHE SETTINGS
# ===================================================================
# Local and distributed cache configuration
cache:
  enable_statistics: true
  default_max_size: 1000
  default_expire_minutes: 30

  # Specific cache configurations
  caches:
    playerDataCache:
      max_size: 500
      expire_minutes: 10

    serverDataCache:
      max_size: 200
      expire_minutes: 5

    punishmentCache:
      max_size: 1000
      expire_minutes: 15

# ===================================================================
#                       SECURITY SETTINGS
# ===================================================================
# Security and rate limiting configuration
security:
  command_cooldown_ms: 100
  max_async_operations_per_player: 10
  rate_limit_enabled: true
  rate_limit_requests_per_minute: 60

# ===================================================================
#                       WEBPANEL SETTINGS
# ===================================================================
# Configuration for webpanel integration (future feature)
webpanel:
  enabled: false
  api_key: "change_this_api_key"
  allowed_ips:
    - "127.0.0.1"
    - "::1"
  port: 8080
  ssl_enabled: false

# ===================================================================
#                       DEBUG SETTINGS
# ===================================================================
# Debug and logging configuration
debug:
  enabled: false
  log_sql_queries: false
  log_cache_operations: false
  log_redis_operations: false
  verbose_module_loading: false

# ===================================================================
#                       MODULE SETTINGS
# ===================================================================
# Global module configuration
modules:
  auto_load: true
  load_timeout_seconds: 30

  # Module-specific settings will be loaded from their own config files
  # in the modules/ subdirectory

# ===================================================================
#                       MESSAGING SETTINGS
# ===================================================================
# Cross-server messaging configuration
messaging:
  enabled: true
  channel_prefix: "asmpvcore"

  # Message channels for different purposes
  channels:
    player_data: "player_data"
    punishment: "punishment"
    server_sync: "server_sync"
    webpanel: "webpanel"

# ===================================================================
#                       PERFORMANCE SETTINGS
# ===================================================================
# Performance optimization settings
performance:
  async_thread_pool_size: 4
  batch_operations: true
  batch_size: 100
  cache_cleanup_interval_minutes: 5
  security_cleanup_interval_minutes: 2
