package de.dasjeff.aSMPVCore.modules.punishment.listeners;

import com.velocitypowered.api.event.PostOrder;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.connection.LoginEvent;
import com.velocitypowered.api.event.player.ServerPreConnectEvent;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.modules.punishment.model.Punishment;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Listener for player connection events to enforce punishments.
 * Handles ban checking and punishment enforcement on login.
 */
public class PlayerConnectionListener {

    private final ASMPVCore corePlugin;
    private final PunishmentModule punishmentModule;

    public PlayerConnectionListener(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        this.corePlugin = corePlugin;
        this.punishmentModule = punishmentModule;
    }

    /**
     * Handles player login events to check for active bans.
     */
    @Subscribe(order = PostOrder.EARLY)
    public void onPlayerLogin(LoginEvent event) {
        Player player = event.getPlayer();

        // Check for active ban asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                // Check if player is banned
                Optional<Punishment> banOpt = punishmentModule.getPunishmentManager().getActiveBan(player.getUniqueId());

                if (banOpt.isPresent()) {
                    Punishment ban = banOpt.get();

                    // Check if ban has expired
                    if (ban.isExpired()) {
                        // Mark as expired
                        punishmentModule.getDataAccessor().updatePunishmentStatus(
                            ban.getId(),
                            de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus.EXPIRED
                        );

                        corePlugin.getLogger().info("Expired ban removed for player {} ({})",
                            player.getUsername(), player.getUniqueId());
                        return;
                    }

                    // Create ban message
                    String banMessage = createBanMessage(ban);

                    // Disconnect the player on the main thread
                    corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                        event.setResult(LoginEvent.ComponentResult.denied(MessageUtil.formatLegacy(banMessage)));

                        corePlugin.getLogger().info("Denied login for banned player {} ({}): {}",
                            player.getUsername(), player.getUniqueId(), ban.getReason());
                    }).schedule();
                }

                // TODO: Check for IP bans in future implementation

            } catch (Exception e) {
                corePlugin.getLogger().error("Error checking ban status for player " + player.getUsername(), e);
            }
        });
    }

    /**
     * Handles server pre-connect events to apply voice mutes.
     */
    @Subscribe(order = PostOrder.NORMAL)
    public void onServerPreConnect(ServerPreConnectEvent event) {
        Player player = event.getPlayer();

        // Apply voice mutes asynchronously
        CompletableFuture.runAsync(() -> {
            try {
                // Check if player has an active voice mute
                Optional<Punishment> voiceMuteOpt = punishmentModule.getVoiceChatManager().getActiveVoiceMute(player.getUniqueId());

                if (voiceMuteOpt.isPresent()) {
                    Punishment voiceMute = voiceMuteOpt.get();

                    // Check if voice mute has expired
                    if (voiceMute.isExpired()) {
                        // Mark as expired and remove voice mute
                        punishmentModule.getDataAccessor().updatePunishmentStatus(
                            voiceMute.getId(),
                            de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus.EXPIRED
                        );
                        punishmentModule.getVoiceChatManager().removeVoiceMute(player.getUniqueId());

                        corePlugin.getLogger().info("Expired voice mute removed for player {} ({})",
                            player.getUsername(), player.getUniqueId());
                        return;
                    }

                    // Apply voice mute
                    boolean success = punishmentModule.getVoiceChatManager().applyVoiceMute(voiceMute);

                    if (success) {
                        corePlugin.getLogger().debug("Applied voice mute to connecting player {} ({})",
                            player.getUsername(), player.getUniqueId());
                    } else {
                        corePlugin.getLogger().warn("Failed to apply voice mute to connecting player {} ({})",
                            player.getUsername(), player.getUniqueId());
                    }
                }

            } catch (Exception e) {
                corePlugin.getLogger().error("Error applying voice mute for player " + player.getUsername(), e);
            }
        });
    }

    /**
     * Creates a ban message for the player.
     */
    private String createBanMessage(Punishment ban) {
        if (ban.isPermanent()) {
            return punishmentModule.getMessageConfig().getPlayerNotification("permanent_ban",
                "{reason}", ban.getReason());
        } else {
            return punishmentModule.getMessageConfig().getPlayerNotification("temporary_ban",
                "{reason}", ban.getReason(),
                "{duration}", ban.getFormattedDuration());
        }
    }
}
