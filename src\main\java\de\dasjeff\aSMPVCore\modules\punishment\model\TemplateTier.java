package de.dasjeff.aSMPVCore.modules.punishment.model;

import java.util.Objects;

/**
 * Represents a single tier within a punishment template.
 * Each tier has an escalating severity level.
 */
public class TemplateTier {
    
    private final int id;
    private final String duration;
    private final String reasonTemplate;

    public TemplateTier(int id, String duration, String reasonTemplate) {
        this.id = id;
        this.duration = Objects.requireNonNull(duration, "Duration cannot be null");
        this.reasonTemplate = Objects.requireNonNull(reasonTemplate, "Reason template cannot be null");
    }

    /**
     * Gets the tier ID (1, 2, 3, etc.).
     */
    public int getId() {
        return id;
    }

    /**
     * Gets the duration string (e.g., "30m", "2h", "permanent").
     */
    public String getDuration() {
        return duration;
    }

    /**
     * Gets the reason template for this tier.
     */
    public String getReasonTemplate() {
        return reasonTemplate;
    }

    /**
     * Checks if this tier represents a permanent punishment.
     */
    public boolean isPermanent() {
        return "permanent".equalsIgnoreCase(duration) || "perm".equalsIgnoreCase(duration);
    }

    /**
     * Gets a formatted display string for this tier.
     */
    public String getDisplayString() {
        return String.format("Tier %d: %s - %s", id, duration, reasonTemplate);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TemplateTier that = (TemplateTier) o;
        return id == that.id &&
               Objects.equals(duration, that.duration) &&
               Objects.equals(reasonTemplate, that.reasonTemplate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, duration, reasonTemplate);
    }

    @Override
    public String toString() {
        return "TemplateTier{" +
               "id=" + id +
               ", duration='" + duration + '\'' +
               ", reasonTemplate='" + reasonTemplate + '\'' +
               '}';
    }
}
