package de.dasjeff.aSMPVCore.messaging;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.managers.CacheManager;
import org.jetbrains.annotations.NotNull;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPubSub;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Manages cross-server messaging using Redis Pub/Sub.
 * Handles message publishing, subscription, and routing to appropriate handlers.
 */
public class MessagingManager {

    private final ASMPVCore corePlugin;
    private final CacheManager cacheManager;
    private final String serverName;
    private final String channelPrefix;
    private final AtomicBoolean initialized = new AtomicBoolean(false);
    private final AtomicBoolean shutdown = new AtomicBoolean(false);

    // Message handling
    private final Map<MessageType, Set<MessageHandler>> messageHandlers = new ConcurrentHashMap<>();
    private final ExecutorService messageProcessingExecutor;
    private final ExecutorService subscriptionExecutor;

    // Redis subscription
    private volatile MessageSubscriber subscriber;
    private final Set<String> subscribedChannels = ConcurrentHashMap.newKeySet();

    // Message statistics
    private volatile long messagesSent = 0;
    private volatile long messagesReceived = 0;
    private volatile long messagesProcessed = 0;
    private volatile long messagesFailed = 0;

    public MessagingManager(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.cacheManager = corePlugin.getCacheManager();
        this.serverName = corePlugin.getConfigManager().getMainConfig().getString("server.name", "velocity-proxy");
        this.channelPrefix = corePlugin.getConfigManager().getMainConfig().getString("redis.channel_prefix", "asmpvcore");

        // Create thread pools for message processing
        this.messageProcessingExecutor = Executors.newFixedThreadPool(
            Math.max(2, Runtime.getRuntime().availableProcessors() / 2),
            r -> {
                Thread t = new Thread(r, "ASMP-MessageProcessor");
                t.setDaemon(true);
                return t;
            }
        );

        this.subscriptionExecutor = Executors.newSingleThreadExecutor(
            r -> {
                Thread t = new Thread(r, "ASMP-RedisSubscriber");
                t.setDaemon(true);
                return t;
            }
        );
    }

    /**
     * Initializes the messaging manager and starts Redis subscription.
     * @return true if initialization was successful, false otherwise.
     */
    public boolean initialize() {
        if (initialized.get()) {
            corePlugin.getLogger().warn("MessagingManager is already initialized");
            return true;
        }

        if (cacheManager == null || !cacheManager.isRedisEnabled()) {
            corePlugin.getLogger().error("Redis is not available - messaging system cannot be initialized");
            return false;
        }

        try {
            // Test Redis connection
            if (!testRedisConnection()) {
                corePlugin.getLogger().error("Redis connection test failed");
                return false;
            }

            // Start subscription
            startSubscription();

            initialized.set(true);
            corePlugin.getLogger().info("MessagingManager initialized successfully");
            return true;

        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to initialize MessagingManager", e);
            return false;
        }
    }

    /**
     * Shuts down the messaging manager and cleans up resources.
     */
    public void shutdown() {
        if (shutdown.getAndSet(true)) {
            return;
        }

        corePlugin.getLogger().info("Shutting down MessagingManager...");

        // Stop subscription
        if (subscriber != null) {
            subscriber.unsubscribe();
        }

        // Shutdown executors
        shutdownExecutor(subscriptionExecutor, "SubscriptionExecutor");
        shutdownExecutor(messageProcessingExecutor, "MessageProcessingExecutor");

        // Clear handlers
        messageHandlers.clear();
        subscribedChannels.clear();

        initialized.set(false);
        corePlugin.getLogger().info("MessagingManager shutdown complete");
    }

    /**
     * Registers a message handler for specific message types.
     * @param handler The message handler to register.
     * @return true if registration was successful, false otherwise.
     */
    public boolean registerHandler(@NotNull MessageHandler handler) {
        if (!initialized.get()) {
            corePlugin.getLogger().warn("Cannot register handler - MessagingManager not initialized");
            return false;
        }

        Set<MessageType> handledTypes = handler.getHandledMessageTypes();
        if (handledTypes.isEmpty()) {
            corePlugin.getLogger().warn("Handler {} has no handled message types", handler.getHandlerName());
            return false;
        }

        for (MessageType type : handledTypes) {
            messageHandlers.computeIfAbsent(type, k -> ConcurrentHashMap.newKeySet()).add(handler);

            // Subscribe to the channel for this message type if not already subscribed
            String channel = type.getChannelName(channelPrefix);
            if (subscribedChannels.add(channel)) {
                subscribeToChannel(channel);
            }
        }

        handler.onRegister();
        corePlugin.getLogger().info("Registered message handler: {} for types: {}",
                                   handler.getHandlerName(), handledTypes);
        return true;
    }

    /**
     * Unregisters a message handler.
     * @param handler The message handler to unregister.
     */
    public void unregisterHandler(@NotNull MessageHandler handler) {
        for (Set<MessageHandler> handlers : messageHandlers.values()) {
            handlers.remove(handler);
        }

        handler.onUnregister();
        corePlugin.getLogger().info("Unregistered message handler: {}", handler.getHandlerName());
    }

    /**
     * Publishes a message to the network.
     * @param message The message to publish.
     * @return true if the message was published successfully, false otherwise.
     */
    public boolean publishMessage(@NotNull NetworkMessage message) {
        if (!initialized.get() || shutdown.get()) {
            corePlugin.getLogger().warn("Cannot publish message - MessagingManager not available");
            return false;
        }

        try {
            String channel = message.getType().getChannelName(channelPrefix);
            String json = message.toJson();

            cacheManager.publishRedis(channel, json);
            messagesSent++;

            corePlugin.getLogger().debug("Published message {} to channel {}",
                                       message.getMessageId(), channel);
            return true;

        } catch (Exception e) {
            messagesFailed++;
            corePlugin.getLogger().error("Failed to publish message {}", message.getMessageId(), e);
            return false;
        }
    }

    /**
     * Creates a message builder for the current server.
     * @param type The message type.
     * @return A new message builder.
     */
    @NotNull
    public NetworkMessage.Builder createMessage(@NotNull MessageType type) {
        return NetworkMessage.builder(type, serverName);
    }

    /**
     * Gets messaging statistics.
     * @return A map containing messaging statistics.
     */
    @NotNull
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("initialized", initialized.get());
        stats.put("shutdown", shutdown.get());
        stats.put("messagesSent", messagesSent);
        stats.put("messagesReceived", messagesReceived);
        stats.put("messagesProcessed", messagesProcessed);
        stats.put("messagesFailed", messagesFailed);
        stats.put("subscribedChannels", subscribedChannels.size());
        stats.put("registeredHandlers", messageHandlers.size());
        return stats;
    }

    /**
     * Checks if the messaging manager is initialized and ready.
     * @return true if initialized and not shutdown, false otherwise.
     */
    public boolean isReady() {
        return initialized.get() && !shutdown.get();
    }

    /**
     * Gets the server name used for messaging.
     * @return The server name.
     */
    @NotNull
    public String getServerName() {
        return serverName;
    }

    /**
     * Gets the channel prefix used for Redis channels.
     * @return The channel prefix.
     */
    @NotNull
    public String getChannelPrefix() {
        return channelPrefix;
    }

    /**
     * Tests the Redis connection.
     */
    private boolean testRedisConnection() {
        if (!cacheManager.isRedisAvailable()) {
            return false;
        }

        // Use the existing Redis methods from CacheManager
        try {
            // Test with a simple ping operation via publish (which internally tests connection)
            cacheManager.publishRedis("test:ping", "ping");
            return true;
        } catch (Exception e) {
            corePlugin.getLogger().error("Redis connection test failed", e);
            return false;
        }
    }

    /**
     * Starts the Redis subscription in a separate thread.
     */
    private void startSubscription() {
        subscriber = new MessageSubscriber();
        subscriptionExecutor.submit(() -> {
            while (!shutdown.get()) {
                try (Jedis jedis = cacheManager.getRedisResource()) {
                    if (jedis != null) {
                        // Subscribe to all message channels
                        String[] channels = {
                            channelPrefix + ":punishment",
                            channelPrefix + ":player_data",
                            channelPrefix + ":cache",
                            channelPrefix + ":staff",
                            channelPrefix + ":system"
                        };

                        corePlugin.getLogger().info("Starting Redis subscription to channels: {}",
                                                   String.join(", ", channels));
                        jedis.subscribe(subscriber, channels);
                    }
                } catch (Exception e) {
                    if (!shutdown.get()) {
                        corePlugin.getLogger().error("Redis subscription failed, retrying in 5 seconds...", e);
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        });
    }

    /**
     * Subscribes to a specific channel (simplified - channels are predefined).
     */
    private void subscribeToChannel(String channel) {
        // Channels are now predefined and subscribed to at startup
        // This method is kept for future extensibility
        corePlugin.getLogger().debug("Channel subscription requested: {}", channel);
    }

    /**
     * Processes an incoming message.
     */
    private void processMessage(String channel, String messageJson) {
        messageProcessingExecutor.submit(() -> {
            try {
                NetworkMessage message = NetworkMessage.fromJson(messageJson);
                if (message == null) {
                    corePlugin.getLogger().warn("Failed to parse message from channel {}", channel);
                    messagesFailed++;
                    return;
                }

                messagesReceived++;

                // Skip messages from our own server
                if (serverName.equals(message.getSourceServer())) {
                    return;
                }

                // Get handlers for this message type
                Set<MessageHandler> handlers = messageHandlers.get(message.getType());
                if (handlers == null || handlers.isEmpty()) {
                    return;
                }

                // Process message with all registered handlers
                for (MessageHandler handler : handlers) {
                    try {
                        if (handler.shouldProcessFromServer(message.getSourceServer()) &&
                            handler.shouldProcessMessageAge(message.getAge())) {

                            if (handler.handleMessage(message)) {
                                messagesProcessed++;
                            }
                        }
                    } catch (Exception e) {
                        messagesFailed++;
                        handler.onHandlingError(message, e);
                        corePlugin.getLogger().error("Handler {} failed to process message {}",
                                                   handler.getHandlerName(), message.getMessageId(), e);
                    }
                }

            } catch (Exception e) {
                messagesFailed++;
                corePlugin.getLogger().error("Failed to process message from channel {}", channel, e);
            }
        });
    }

    /**
     * Shuts down an executor service gracefully.
     */
    private void shutdownExecutor(ExecutorService executor, String name) {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    corePlugin.getLogger().warn("{} did not terminate gracefully", name);
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Redis subscriber implementation.
     */
    private class MessageSubscriber extends JedisPubSub {
        @Override
        public void onMessage(String channel, String message) {
            processMessage(channel, message);
        }

        @Override
        public void onSubscribe(String channel, int subscribedChannels) {
            corePlugin.getLogger().debug("Subscribed to Redis channel: {}", channel);
        }

        @Override
        public void onUnsubscribe(String channel, int subscribedChannels) {
            corePlugin.getLogger().debug("Unsubscribed from Redis channel: {}", channel);
        }
    }
}
