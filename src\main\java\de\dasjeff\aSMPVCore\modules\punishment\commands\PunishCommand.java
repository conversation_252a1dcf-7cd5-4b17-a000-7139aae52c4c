package de.dasjeff.aSMPVCore.modules.punishment.commands;

import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.commands.AbstractCommand;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Main punishment command for opening the punishment GUI.
 * Usage: /punish <player>
 */
public class PunishCommand extends AbstractCommand {

    private final PunishmentModule punishmentModule;

    public PunishCommand(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        super(corePlugin, "punish", "asmp.vcore.punishment.access", "Open punishment GUI for a player");
        this.punishmentModule = punishmentModule;
    }

    @Override
    protected void executeCommand(CommandSource source, String[] args) {
        // Only players can use this command (GUI-based)
        if (!(source instanceof Player player)) {
            source.sendMessage(MessageUtil.prefixedError("This command can only be used by players."));
            return;
        }

        // Check permission
        if (!punishmentModule.getPermissionUtil().hasAccess(player)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getCommandMessage("punish", "no_permission")
            ));
            return;
        }

        // Validate arguments
        if (args.length != 1) {
            source.sendMessage(MessageUtil.prefixedError("Usage: /punish <player>"));
            return;
        }

        String targetPlayerName = args[0];

        // Security check
        if (!corePlugin.getSecurityManager().isInputSafe(targetPlayerName)) {
            source.sendMessage(MessageUtil.formatLegacy(
                punishmentModule.getMessageConfig().getErrorMessage("invalid_input", "{input}", targetPlayerName)
            ));
            return;
        }

        // Rate limiting
        if (!corePlugin.getSecurityManager().canExecuteCommand(player.getUniqueId())) {
            long remainingCooldown = corePlugin.getSecurityManager().getRemainingCooldown(player.getUniqueId());
            source.sendMessage(MessageUtil.prefixedError("Please wait " + remainingCooldown + "ms before using this command again."));
            return;
        }

        // Execute asynchronously to avoid blocking
        CompletableFuture.runAsync(() -> {
            try {
                // Check if target player exists
                // TODO: Implement player lookup and GUI opening
                // For now, just send a placeholder message
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.prefixedInfo("Opening punishment GUI for " + targetPlayerName + "..."));
                    source.sendMessage(MessageUtil.prefixedInfo("GUI system will be implemented in the next phase."));
                }).schedule();

            } catch (Exception e) {
                corePlugin.getLogger().error("Error executing punish command", e);
                corePlugin.getProxyServer().getScheduler().buildTask(corePlugin, () -> {
                    source.sendMessage(MessageUtil.formatLegacy(
                        punishmentModule.getMessageConfig().getErrorMessage("internal_error")
                    ));
                }).schedule();
            }
        });
    }

    @Override
    protected String getUsage() {
        return "/punish <player>";
    }

    @Override
    protected List<String> getTabCompletions(CommandSource source, String[] args) {
        if (args.length == 1) {
            // Suggest online player names
            String partial = args[0].toLowerCase();
            return corePlugin.getProxyServer().getAllPlayers().stream()
                    .map(Player::getUsername)
                    .filter(name -> name.toLowerCase().startsWith(partial))
                    .sorted()
                    .toList();
        }
        return List.of();
    }
}
