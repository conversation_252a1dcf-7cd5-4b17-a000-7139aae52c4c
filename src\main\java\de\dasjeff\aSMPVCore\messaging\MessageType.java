package de.dasjeff.aSMPVCore.messaging;

/**
 * Enumeration of all message types used in cross-server communication.
 * Each message type corresponds to a specific action or event that needs
 * to be synchronized across the network.
 */
public enum MessageType {
    
    // Punishment-related messages
    PUNISHMENT_CREATED("punishment_created"),
    PUNISHMENT_UPDATED("punishment_updated"),
    PUNISHMENT_EXPIRED("punishment_expired"),
    PUNISHMENT_PARDONED("punishment_pardoned"),
    PUNISHMENT_DELETED("punishment_deleted"),
    
    // IP Ban messages
    IP_BAN_CREATED("ip_ban_created"),
    IP_BAN_UPDATED("ip_ban_updated"),
    IP_BAN_EXPIRED("ip_ban_expired"),
    IP_BAN_PARDONED("ip_ban_pardoned"),
    
    // Player data synchronization
    PLAYER_DATA_UPDATED("player_data_updated"),
    PLAYER_JOINED("player_joined"),
    PLAYER_LEFT("player_left"),
    
    // Cache management
    CACHE_INVALIDATE("cache_invalidate"),
    CACHE_REFRESH("cache_refresh"),
    
    // Staff notifications
    STAFF_ALERT("staff_alert"),
    STAFF_NOTIFICATION("staff_notification"),
    
    // System messages
    SERVER_STATUS("server_status"),
    HEARTBEAT("heartbeat"),
    
    // Module communication
    MODULE_RELOAD("module_reload"),
    CONFIG_RELOAD("config_reload");
    
    private final String messageId;
    
    MessageType(String messageId) {
        this.messageId = messageId;
    }
    
    /**
     * Gets the string identifier for this message type.
     * @return The message identifier used in JSON serialization.
     */
    public String getMessageId() {
        return messageId;
    }
    
    /**
     * Gets the Redis channel name for this message type.
     * @param channelPrefix The configured channel prefix.
     * @return The full Redis channel name.
     */
    public String getChannelName(String channelPrefix) {
        return channelPrefix + ":" + getChannelCategory();
    }
    
    /**
     * Gets the category/channel for this message type.
     * @return The channel category this message belongs to.
     */
    public String getChannelCategory() {
        return switch (this) {
            case PUNISHMENT_CREATED, PUNISHMENT_UPDATED, PUNISHMENT_EXPIRED, 
                 PUNISHMENT_PARDONED, PUNISHMENT_DELETED,
                 IP_BAN_CREATED, IP_BAN_UPDATED, IP_BAN_EXPIRED, IP_BAN_PARDONED -> "punishment";
                 
            case PLAYER_DATA_UPDATED, PLAYER_JOINED, PLAYER_LEFT -> "player_data";
            
            case CACHE_INVALIDATE, CACHE_REFRESH -> "cache";
            
            case STAFF_ALERT, STAFF_NOTIFICATION -> "staff";
            
            case SERVER_STATUS, HEARTBEAT, MODULE_RELOAD, CONFIG_RELOAD -> "system";
        };
    }
    
    /**
     * Finds a MessageType by its message ID.
     * @param messageId The message ID to search for.
     * @return The corresponding MessageType, or null if not found.
     */
    public static MessageType fromMessageId(String messageId) {
        if (messageId == null) {
            return null;
        }
        
        for (MessageType type : values()) {
            if (type.messageId.equals(messageId)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * Checks if this message type is punishment-related.
     * @return true if this is a punishment message, false otherwise.
     */
    public boolean isPunishmentMessage() {
        return getChannelCategory().equals("punishment");
    }
    
    /**
     * Checks if this message type is player-data-related.
     * @return true if this is a player data message, false otherwise.
     */
    public boolean isPlayerDataMessage() {
        return getChannelCategory().equals("player_data");
    }
    
    /**
     * Checks if this message type is cache-related.
     * @return true if this is a cache message, false otherwise.
     */
    public boolean isCacheMessage() {
        return getChannelCategory().equals("cache");
    }
    
    /**
     * Checks if this message type is staff-related.
     * @return true if this is a staff message, false otherwise.
     */
    public boolean isStaffMessage() {
        return getChannelCategory().equals("staff");
    }
    
    /**
     * Checks if this message type is system-related.
     * @return true if this is a system message, false otherwise.
     */
    public boolean isSystemMessage() {
        return getChannelCategory().equals("system");
    }
    
    /**
     * Gets the priority level for this message type.
     * Higher priority messages should be processed first.
     * @return Priority level (1-5, where 5 is highest priority).
     */
    public int getPriority() {
        return switch (this) {
            // High priority - immediate action required
            case PUNISHMENT_CREATED, IP_BAN_CREATED -> 5;
            
            // Medium-high priority - enforcement updates
            case PUNISHMENT_UPDATED, PUNISHMENT_EXPIRED, PUNISHMENT_PARDONED,
                 IP_BAN_UPDATED, IP_BAN_EXPIRED, IP_BAN_PARDONED -> 4;
            
            // Medium priority - player data and cache
            case PLAYER_JOINED, PLAYER_LEFT, CACHE_INVALIDATE -> 3;
            
            // Low-medium priority - notifications and updates
            case STAFF_ALERT, STAFF_NOTIFICATION, PLAYER_DATA_UPDATED -> 2;
            
            // Low priority - system maintenance
            case HEARTBEAT, SERVER_STATUS, MODULE_RELOAD, CONFIG_RELOAD,
                 CACHE_REFRESH, PUNISHMENT_DELETED -> 1;
        };
    }
}
