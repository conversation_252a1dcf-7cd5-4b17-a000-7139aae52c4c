plugins {
    id 'java'
    id 'eclipse'
    id 'org.jetbrains.gradle.plugin.idea-ext' version '1.1.8'
    id("xyz.jpenilla.run-velocity") version "2.3.1"
    id("com.gradleup.shadow") version "9.0.0-beta13"
}

group = 'de.dasjeff'
version = '1.0'

repositories {
    mavenCentral()
    maven {
        name = "velocity-repo"
        url = "https://nexus.velocitypowered.com/repository/maven-public/"
    }
    maven {
        name = "sonatype"
        url = "https://oss.sonatype.org/content/groups/public/"
    }
    maven {
        name = "maxhenkel-repo"
        url = "https://maven.maxhenkel.de/repository/public"
    }
}

dependencies {
    compileOnly("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")
    annotationProcessor("com.velocitypowered:velocity-api:3.4.0-SNAPSHOT")

    // Voice Chat API for punishment system
    implementation('de.maxhenkel.voicechat:voicechat-api:2.5.27')

    // Database
    implementation('com.zaxxer:HikariCP:5.1.0')
    implementation('org.mariadb.jdbc:mariadb-java-client:3.3.3')

    // Caching
    implementation('com.github.ben-manes.caffeine:caffeine:3.1.8')
    implementation('redis.clients:jedis:5.1.0')

    // Configuration
    implementation('org.yaml:snakeyaml:2.2')

    // Utilities
    compileOnly('org.jetbrains:annotations:24.1.0')
}

tasks {
    runVelocity {
        // Configure the Velocity version for our task.
        // This is the only required configuration besides applying the plugin.
        // Your plugin's jar (or shadowJar if present) will be used automatically.
        velocityVersion("3.4.0-SNAPSHOT")
    }
}

def targetJavaVersion = 21
java {
    toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
    options.release.set(targetJavaVersion)
}

def templateSource = file('src/main/templates')
def templateDest = layout.buildDirectory.dir('generated/sources/templates')
def generateTemplates = tasks.register('generateTemplates', Copy) { task ->
    def props = ['version': project.version]
    task.inputs.properties props

    task.from templateSource
    task.into templateDest
    task.expand props
}

sourceSets.main.java.srcDir(generateTemplates.map { it.outputs })

project.idea.project.settings.taskTriggers.afterSync generateTemplates
project.eclipse.synchronizationTasks(generateTemplates)

shadowJar {
    archiveClassifier.set('')

    relocate 'com.zaxxer.hikari', 'de.dasjeff.aSMPVCore.lib.hikaricp'
    relocate 'org.mariadb.jdbc', 'de.dasjeff.aSMPVCore.lib.mariadb'
    relocate 'com.github.benmanes.caffeine', 'de.dasjeff.aSMPVCore.lib.caffeine'
    relocate 'redis.clients.jedis', 'de.dasjeff.aSMPVCore.lib.jedis'
    relocate 'org.yaml.snakeyaml', 'de.dasjeff.aSMPVCore.lib.snakeyaml'

    exclude 'com/velocitypowered/**'
    exclude 'META-INF/LICENSE'
    exclude 'META-INF/LICENSE.txt'
    exclude 'META-INF/NOTICE'
    exclude 'META-INF/NOTICE.txt'
    exclude 'META-INF/AL2.0'
    exclude 'META-INF/LGPL2.1'
    exclude 'META-INF/DEPENDENCIES'
}

tasks.build {
    dependsOn tasks.shadowJar
}
