package de.dasjeff.aSMPVCore.modules.punishment.database;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.modules.punishment.model.Punishment;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentType;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Data access layer for punishment system.
 * Handles all database operations for punishments, IP bans, and staff actions.
 */
public class PunishmentDataAccessor {

    private final ASMPVCore corePlugin;
    private final PunishmentModule punishmentModule;

    public PunishmentDataAccessor(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        this.corePlugin = corePlugin;
        this.punishmentModule = punishmentModule;
    }

    /**
     * Creates a new punishment record in the database.
     */
    public boolean createPunishment(Punishment punishment) {
        String sql = """
            INSERT INTO asmp_punishments (
                player_uuid, punishment_type, reason, staff_uuid, staff_name,
                internal_note, template_name, template_stufe_id, start_time,
                end_time, duration_string, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

        try (Connection conn = corePlugin.getDatabaseManager().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, punishment.getPlayerUuid().toString());
            pstmt.setString(2, punishment.getType().name());
            pstmt.setString(3, punishment.getReason());
            pstmt.setString(4, punishment.getStaffUuid().toString());
            pstmt.setString(5, punishment.getStaffName());
            pstmt.setString(6, punishment.getInternalNote());
            pstmt.setString(7, punishment.getTemplateName());
            
            if (punishment.getTemplateTierId() != null) {
                pstmt.setInt(8, punishment.getTemplateTierId());
            } else {
                pstmt.setNull(8, java.sql.Types.INTEGER);
            }
            
            pstmt.setTimestamp(9, java.sql.Timestamp.valueOf(punishment.getStartTime()));
            
            if (punishment.getEndTime() != null) {
                pstmt.setTimestamp(10, java.sql.Timestamp.valueOf(punishment.getEndTime()));
            } else {
                pstmt.setNull(10, java.sql.Types.TIMESTAMP);
            }
            
            pstmt.setString(11, punishment.getDurationString());
            pstmt.setString(12, punishment.getStatus().name());

            return pstmt.executeUpdate() > 0;

        } catch (SQLException e) {
            corePlugin.getLogger().error("Failed to create punishment record", e);
            return false;
        }
    }

    /**
     * Gets active punishments for a player by type.
     */
    public List<Punishment> getActivePunishments(UUID playerUuid, PunishmentType type) {
        String sql = """
            SELECT * FROM asmp_punishments 
            WHERE player_uuid = ? AND punishment_type = ? AND status = 'ACTIVE'
            ORDER BY created_at DESC
            """;

        List<Punishment> punishments = new ArrayList<>();

        try (Connection conn = corePlugin.getDatabaseManager().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, type.name());

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    punishments.add(mapResultSetToPunishment(rs));
                }
            }

        } catch (SQLException e) {
            corePlugin.getLogger().error("Failed to get active punishments", e);
        }

        return punishments;
    }

    /**
     * Gets all punishments for a player.
     */
    public List<Punishment> getPunishmentHistory(UUID playerUuid) {
        String sql = """
            SELECT * FROM asmp_punishments 
            WHERE player_uuid = ? 
            ORDER BY created_at DESC
            """;

        List<Punishment> punishments = new ArrayList<>();

        try (Connection conn = corePlugin.getDatabaseManager().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, playerUuid.toString());

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    punishments.add(mapResultSetToPunishment(rs));
                }
            }

        } catch (SQLException e) {
            corePlugin.getLogger().error("Failed to get punishment history", e);
        }

        return punishments;
    }

    /**
     * Gets the current tier count for a specific template and player.
     */
    public int getTemplateTierCount(UUID playerUuid, String templateName) {
        String sql = """
            SELECT COUNT(*) FROM asmp_punishments 
            WHERE player_uuid = ? AND template_name = ? AND status != 'PARDONED'
            """;

        try (Connection conn = corePlugin.getDatabaseManager().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, playerUuid.toString());
            pstmt.setString(2, templateName);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }

        } catch (SQLException e) {
            corePlugin.getLogger().error("Failed to get template tier count", e);
        }

        return 0;
    }

    /**
     * Updates the status of a punishment.
     */
    public boolean updatePunishmentStatus(int punishmentId, PunishmentStatus status) {
        String sql = """
            UPDATE asmp_punishments 
            SET status = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
            """;

        try (Connection conn = corePlugin.getDatabaseManager().getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status.name());
            pstmt.setInt(2, punishmentId);

            return pstmt.executeUpdate() > 0;

        } catch (SQLException e) {
            corePlugin.getLogger().error("Failed to update punishment status", e);
            return false;
        }
    }

    /**
     * Checks if a player has an active ban.
     */
    public Optional<Punishment> getActiveBan(UUID playerUuid) {
        List<Punishment> bans = getActivePunishments(playerUuid, PunishmentType.BAN);
        return bans.isEmpty() ? Optional.empty() : Optional.of(bans.get(0));
    }

    /**
     * Checks if a player has an active mute.
     */
    public Optional<Punishment> getActiveMute(UUID playerUuid) {
        List<Punishment> mutes = getActivePunishments(playerUuid, PunishmentType.MUTE);
        return mutes.isEmpty() ? Optional.empty() : Optional.of(mutes.get(0));
    }

    /**
     * Checks if a player has an active voice mute.
     */
    public Optional<Punishment> getActiveVoiceMute(UUID playerUuid) {
        List<Punishment> voiceMutes = getActivePunishments(playerUuid, PunishmentType.VOICE_MUTE);
        return voiceMutes.isEmpty() ? Optional.empty() : Optional.of(voiceMutes.get(0));
    }

    /**
     * Maps a ResultSet row to a Punishment object.
     */
    private Punishment mapResultSetToPunishment(ResultSet rs) throws SQLException {
        return new Punishment(
            rs.getInt("id"),
            UUID.fromString(rs.getString("player_uuid")),
            PunishmentType.valueOf(rs.getString("punishment_type")),
            rs.getString("reason"),
            UUID.fromString(rs.getString("staff_uuid")),
            rs.getString("staff_name"),
            rs.getString("internal_note"),
            rs.getString("template_name"),
            rs.getObject("template_stufe_id", Integer.class),
            rs.getTimestamp("start_time").toLocalDateTime(),
            rs.getTimestamp("end_time") != null ? rs.getTimestamp("end_time").toLocalDateTime() : null,
            rs.getString("duration_string"),
            PunishmentStatus.valueOf(rs.getString("status")),
            rs.getTimestamp("created_at").toLocalDateTime(),
            rs.getTimestamp("updated_at").toLocalDateTime()
        );
    }

    // TODO: Implement IP ban methods
    // TODO: Implement staff action logging methods
    // TODO: Implement punishment statistics methods
}
