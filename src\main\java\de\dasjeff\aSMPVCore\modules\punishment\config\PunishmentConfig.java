package de.dasjeff.aSMPVCore.modules.punishment.config;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentTemplate;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentType;
import de.dasjeff.aSMPVCore.modules.punishment.model.TemplateTier;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Configuration manager for punishment templates and settings.
 * Handles loading, parsing, and validation of punishment configuration.
 */
public class PunishmentConfig {

    private final ASMPVCore corePlugin;
    private final Path configDirectory;
    private final Yaml yaml;
    
    private Map<String, Object> config;
    private Map<String, PunishmentTemplate> templates;
    private DiscordConfig discordConfig;

    public PunishmentConfig(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.configDirectory = corePlugin.getDataDirectory().resolve("PunishmentSystem");
        this.yaml = new Yaml();
        this.templates = new HashMap<>();
        
        loadConfiguration();
    }

    /**
     * Loads the punishment configuration from file.
     */
    private void loadConfiguration() {
        try {
            // Create directory if it doesn't exist
            Files.createDirectories(configDirectory);
            
            Path configFile = configDirectory.resolve("punishments.yml");
            
            // Create default config if it doesn't exist
            if (!Files.exists(configFile)) {
                createDefaultConfig(configFile);
            }
            
            // Load configuration
            try (FileInputStream fis = new FileInputStream(configFile.toFile())) {
                config = yaml.load(fis);
                if (config == null) {
                    config = new HashMap<>();
                }
            }
            
            // Parse templates
            parseTemplates();
            
            // Parse discord configuration
            parseDiscordConfig();
            
            corePlugin.getLogger().info("[PunishmentSystem] Configuration loaded successfully. {} templates loaded.", templates.size());
            
        } catch (Exception e) {
            corePlugin.getLogger().error("[PunishmentSystem] Failed to load punishment configuration!", e);
            throw new RuntimeException("Failed to load punishment configuration", e);
        }
    }

    /**
     * Creates the default punishment configuration file.
     */
    private void createDefaultConfig(Path configFile) throws IOException {
        String defaultConfig = """
            # ===================================================================
            #                    ASMP-VCore Punishment Configuration
            # ===================================================================
            # Template-based punishment system with escalating tiers
            
            # Definition of punishment templates
            punishment_templates:
              chat_spam:
                display_name: "Chat: Spamming"
                type: MUTE
                permission_node: "asmp.vcore.punishment.template.chat_spam"
                stufen:
                  - id: 1
                    duration: "10m"
                    reason_template: "Spamming im Chat"
                  - id: 2
                    duration: "1h"
                    reason_template: "Wiederholtes Spamming im Chat"
                  - id: 3
                    duration: "6h"
                    reason_template: "Mehrfaches Spamming im Chat"
            
              voice_noise:
                display_name: "Voice: Störende Geräusche"
                type: VOICE_MUTE
                permission_node: "asmp.vcore.punishment.template.voice_noise"
                stufen:
                  - id: 1
                    duration: "30m"
                    reason_template: "Störende Geräusche im Voicechat"
                  - id: 2
                    duration: "2h"
                    reason_template: "Wiederholt störende Geräusche im Voicechat"
                  - id: 3
                    duration: "permanent"
                    reason_template: "Permanenter Voicechat-Ausschluss wegen wiederholter Störung"
            
              ban_hacking:
                display_name: "Ban: Hacking / Client-Modifikationen"
                type: BAN
                ip_ban: true
                permission_node: "asmp.vcore.punishment.template.ban_hacking"
                stufen:
                  - id: 1
                    duration: "permanent"
                    reason_template: "Nutzung unerlaubter Client-Modifikationen (Hacking)"
            
              chat_toxicity:
                display_name: "Chat: Toxisches Verhalten"
                type: MUTE
                permission_node: "asmp.vcore.punishment.template.chat_toxicity"
                stufen:
                  - id: 1
                    duration: "2h"
                    reason_template: "Toxisches Verhalten im Chat"
                  - id: 2
                    duration: "12h"
                    reason_template: "Wiederholtes toxisches Verhalten"
                  - id: 3
                    duration: "3d"
                    reason_template: "Anhaltendes toxisches Verhalten"
            
              ban_griefing:
                display_name: "Ban: Griefing"
                type: BAN
                permission_node: "asmp.vcore.punishment.template.ban_griefing"
                stufen:
                  - id: 1
                    duration: "7d"
                    reason_template: "Griefing von Spielerstrukturen"
                  - id: 2
                    duration: "30d"
                    reason_template: "Wiederholtes Griefing"
                  - id: 3
                    duration: "permanent"
                    reason_template: "Schwerwiegendes und wiederholtes Griefing"
            
            # Discord notification settings
            discord_notifications:
              enabled: true
              webhook_url: "DEIN_DISCORD_WEBHOOK_URL_HIER"
              notify_on:
                - BAN_PERMANENT
                - BAN_TEMPORARY
                - MUTE
              embed_color: "#FF0000"
            """;
        
        try (FileWriter writer = new FileWriter(configFile.toFile())) {
            writer.write(defaultConfig);
        }
        
        corePlugin.getLogger().info("[PunishmentSystem] Created default punishment configuration.");
    }

    /**
     * Parses punishment templates from configuration.
     */
    @SuppressWarnings("unchecked")
    private void parseTemplates() {
        templates.clear();
        
        Map<String, Object> templatesConfig = (Map<String, Object>) config.get("punishment_templates");
        if (templatesConfig == null) {
            corePlugin.getLogger().warn("[PunishmentSystem] No punishment templates found in configuration!");
            return;
        }
        
        for (Map.Entry<String, Object> entry : templatesConfig.entrySet()) {
            String templateName = entry.getKey();
            Map<String, Object> templateData = (Map<String, Object>) entry.getValue();
            
            try {
                PunishmentTemplate template = parseTemplate(templateName, templateData);
                templates.put(templateName, template);
            } catch (Exception e) {
                corePlugin.getLogger().error("[PunishmentSystem] Failed to parse template '{}': {}", templateName, e.getMessage());
            }
        }
    }

    /**
     * Parses a single punishment template.
     */
    @SuppressWarnings("unchecked")
    private PunishmentTemplate parseTemplate(String name, Map<String, Object> data) {
        String displayName = (String) data.get("display_name");
        String typeStr = (String) data.get("type");
        String permissionNode = (String) data.get("permission_node");
        Boolean ipBan = (Boolean) data.get("ip_ban");
        
        if (displayName == null || typeStr == null || permissionNode == null) {
            throw new IllegalArgumentException("Missing required fields for template: " + name);
        }
        
        PunishmentType type;
        try {
            type = PunishmentType.valueOf(typeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid punishment type: " + typeStr);
        }
        
        List<Map<String, Object>> stufenData = (List<Map<String, Object>>) data.get("stufen");
        if (stufenData == null || stufenData.isEmpty()) {
            throw new IllegalArgumentException("No tiers defined for template: " + name);
        }
        
        List<TemplateTier> tiers = new ArrayList<>();
        for (Map<String, Object> tierData : stufenData) {
            TemplateTier tier = parseTier(tierData);
            tiers.add(tier);
        }
        
        return new PunishmentTemplate(
            name,
            displayName,
            type,
            permissionNode,
            ipBan != null && ipBan,
            tiers
        );
    }

    /**
     * Parses a single template tier.
     */
    private TemplateTier parseTier(Map<String, Object> data) {
        Integer id = (Integer) data.get("id");
        String duration = (String) data.get("duration");
        String reasonTemplate = (String) data.get("reason_template");
        
        if (id == null || duration == null || reasonTemplate == null) {
            throw new IllegalArgumentException("Missing required fields for tier");
        }
        
        return new TemplateTier(id, duration, reasonTemplate);
    }

    /**
     * Parses Discord configuration.
     */
    @SuppressWarnings("unchecked")
    private void parseDiscordConfig() {
        Map<String, Object> discordData = (Map<String, Object>) config.get("discord_notifications");
        if (discordData == null) {
            this.discordConfig = new DiscordConfig(false, null, new ArrayList<>(), "#FF0000");
            return;
        }
        
        Boolean enabled = (Boolean) discordData.get("enabled");
        String webhookUrl = (String) discordData.get("webhook_url");
        List<String> notifyOn = (List<String>) discordData.get("notify_on");
        String embedColor = (String) discordData.get("embed_color");
        
        this.discordConfig = new DiscordConfig(
            enabled != null && enabled,
            webhookUrl,
            notifyOn != null ? notifyOn : new ArrayList<>(),
            embedColor != null ? embedColor : "#FF0000"
        );
    }

    /**
     * Reloads the configuration from file.
     */
    public void reload() {
        loadConfiguration();
        corePlugin.getLogger().info("[PunishmentSystem] Configuration reloaded successfully.");
    }

    // Getters
    public Map<String, PunishmentTemplate> getTemplates() {
        return new HashMap<>(templates);
    }

    public PunishmentTemplate getTemplate(String name) {
        return templates.get(name);
    }

    public DiscordConfig getDiscordConfig() {
        return discordConfig;
    }

    /**
     * Discord notification configuration.
     */
    public static class DiscordConfig {
        private final boolean enabled;
        private final String webhookUrl;
        private final List<String> notifyOn;
        private final String embedColor;

        public DiscordConfig(boolean enabled, String webhookUrl, List<String> notifyOn, String embedColor) {
            this.enabled = enabled;
            this.webhookUrl = webhookUrl;
            this.notifyOn = notifyOn;
            this.embedColor = embedColor;
        }

        public boolean isEnabled() { return enabled; }
        public String getWebhookUrl() { return webhookUrl; }
        public List<String> getNotifyOn() { return notifyOn; }
        public String getEmbedColor() { return embedColor; }
    }
}
