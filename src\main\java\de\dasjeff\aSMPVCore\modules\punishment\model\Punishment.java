package de.dasjeff.aSMPVCore.modules.punishment.model;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Represents a punishment record in the database.
 * Contains all information about a specific punishment applied to a player.
 */
public class Punishment {
    
    private final int id;
    private final UUID playerUuid;
    private final PunishmentType type;
    private final String reason;
    private final UUID staffUuid;
    private final String staffName;
    private final String internalNote;
    private final String templateName;
    private final Integer templateTierId;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final String durationString;
    private PunishmentStatus status;
    private final LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructor for creating new punishments
    public Punishment(UUID playerUuid, PunishmentType type, String reason, UUID staffUuid, 
                     String staffName, String internalNote, String templateName, 
                     Integer templateTierId, LocalDateTime startTime, LocalDateTime endTime, 
                     String durationString) {
        this(0, playerUuid, type, reason, staffUuid, staffName, internalNote, templateName,
             templateTierId, startTime, endTime, durationString, PunishmentStatus.ACTIVE,
             LocalDateTime.now(), LocalDateTime.now());
    }

    // Constructor for loading from database
    public Punishment(int id, UUID playerUuid, PunishmentType type, String reason, 
                     UUID staffUuid, String staffName, String internalNote, String templateName,
                     Integer templateTierId, LocalDateTime startTime, LocalDateTime endTime,
                     String durationString, PunishmentStatus status, LocalDateTime createdAt,
                     LocalDateTime updatedAt) {
        this.id = id;
        this.playerUuid = Objects.requireNonNull(playerUuid, "Player UUID cannot be null");
        this.type = Objects.requireNonNull(type, "Type cannot be null");
        this.reason = Objects.requireNonNull(reason, "Reason cannot be null");
        this.staffUuid = Objects.requireNonNull(staffUuid, "Staff UUID cannot be null");
        this.staffName = Objects.requireNonNull(staffName, "Staff name cannot be null");
        this.internalNote = internalNote;
        this.templateName = templateName;
        this.templateTierId = templateTierId;
        this.startTime = Objects.requireNonNull(startTime, "Start time cannot be null");
        this.endTime = endTime;
        this.durationString = Objects.requireNonNull(durationString, "Duration string cannot be null");
        this.status = Objects.requireNonNull(status, "Status cannot be null");
        this.createdAt = Objects.requireNonNull(createdAt, "Created at cannot be null");
        this.updatedAt = Objects.requireNonNull(updatedAt, "Updated at cannot be null");
    }

    // Getters
    public int getId() {
        return id;
    }

    public UUID getPlayerUuid() {
        return playerUuid;
    }

    public PunishmentType getType() {
        return type;
    }

    public String getReason() {
        return reason;
    }

    public UUID getStaffUuid() {
        return staffUuid;
    }

    public String getStaffName() {
        return staffName;
    }

    public String getInternalNote() {
        return internalNote;
    }

    public String getTemplateName() {
        return templateName;
    }

    public Integer getTemplateTierId() {
        return templateTierId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public String getDurationString() {
        return durationString;
    }

    public PunishmentStatus getStatus() {
        return status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    // Status management
    public void setStatus(PunishmentStatus status) {
        this.status = Objects.requireNonNull(status, "Status cannot be null");
        this.updatedAt = LocalDateTime.now();
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = Objects.requireNonNull(updatedAt, "Updated at cannot be null");
    }

    // Utility methods
    public boolean isActive() {
        return status == PunishmentStatus.ACTIVE;
    }

    public boolean isPermanent() {
        return endTime == null || "permanent".equalsIgnoreCase(durationString) || "perm".equalsIgnoreCase(durationString);
    }

    public boolean isExpired() {
        if (isPermanent()) {
            return false;
        }
        return endTime != null && LocalDateTime.now().isAfter(endTime);
    }

    public boolean isFromTemplate() {
        return templateName != null && templateTierId != null;
    }

    public boolean hasInternalNote() {
        return internalNote != null && !internalNote.trim().isEmpty();
    }

    /**
     * Gets the remaining time in milliseconds, or -1 if permanent.
     */
    public long getRemainingTimeMillis() {
        if (isPermanent() || !isActive()) {
            return -1;
        }
        
        if (endTime == null) {
            return -1;
        }
        
        long remaining = java.time.Duration.between(LocalDateTime.now(), endTime).toMillis();
        return Math.max(0, remaining);
    }

    /**
     * Gets a formatted display string for the punishment duration.
     */
    public String getFormattedDuration() {
        if (isPermanent()) {
            return "Permanent";
        }
        return durationString;
    }

    /**
     * Gets a formatted display string for the punishment.
     */
    public String getDisplayString() {
        StringBuilder sb = new StringBuilder();
        sb.append(type.getDisplayName());
        
        if (isFromTemplate()) {
            sb.append(" (").append(templateName).append(" T").append(templateTierId).append(")");
        }
        
        sb.append(" - ").append(getFormattedDuration());
        sb.append(" by ").append(staffName);
        
        return sb.toString();
    }

    /**
     * Creates a copy of this punishment with a new status.
     */
    public Punishment withStatus(PunishmentStatus newStatus) {
        return new Punishment(id, playerUuid, type, reason, staffUuid, staffName, internalNote,
                            templateName, templateTierId, startTime, endTime, durationString,
                            newStatus, createdAt, LocalDateTime.now());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Punishment that = (Punishment) o;
        return id == that.id &&
               Objects.equals(playerUuid, that.playerUuid) &&
               type == that.type &&
               Objects.equals(reason, that.reason) &&
               Objects.equals(staffUuid, that.staffUuid) &&
               Objects.equals(startTime, that.startTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, playerUuid, type, reason, staffUuid, startTime);
    }

    @Override
    public String toString() {
        return "Punishment{" +
               "id=" + id +
               ", playerUuid=" + playerUuid +
               ", type=" + type +
               ", reason='" + reason + '\'' +
               ", staffName='" + staffName + '\'' +
               ", templateName='" + templateName + '\'' +
               ", templateTierId=" + templateTierId +
               ", durationString='" + durationString + '\'' +
               ", status=" + status +
               '}';
    }
}
