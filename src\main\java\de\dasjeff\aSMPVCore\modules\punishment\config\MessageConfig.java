package de.dasjeff.aSMPVCore.modules.punishment.config;

import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.util.MessageUtil;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration manager for punishment system messages.
 * Handles all user-facing messages with placeholder support.
 */
public class MessageConfig {

    private final ASMPVCore corePlugin;
    private final Path configDirectory;
    private final Yaml yaml;
    
    private Map<String, Object> config;

    public MessageConfig(ASMPVCore corePlugin) {
        this.corePlugin = corePlugin;
        this.configDirectory = corePlugin.getDataDirectory().resolve("PunishmentSystem");
        this.yaml = new Yaml();
        
        loadConfiguration();
    }

    /**
     * Loads the message configuration from file.
     */
    private void loadConfiguration() {
        try {
            // Create directory if it doesn't exist
            Files.createDirectories(configDirectory);
            
            Path configFile = configDirectory.resolve("messages.yml");
            
            // Create default config if it doesn't exist
            if (!Files.exists(configFile)) {
                createDefaultConfig(configFile);
            }
            
            // Load configuration
            try (FileInputStream fis = new FileInputStream(configFile.toFile())) {
                config = yaml.load(fis);
                if (config == null) {
                    config = new HashMap<>();
                }
            }
            
            corePlugin.getLogger().info("[PunishmentSystem] Message configuration loaded successfully.");
            
        } catch (Exception e) {
            corePlugin.getLogger().error("[PunishmentSystem] Failed to load message configuration!", e);
            throw new RuntimeException("Failed to load message configuration", e);
        }
    }

    /**
     * Creates the default message configuration file.
     */
    private void createDefaultConfig(Path configFile) throws IOException {
        String defaultConfig = """
            # ===================================================================
            #                    ASMP-VCore Punishment Messages
            # ===================================================================
            # All messages support MiniMessage format and placeholders
            
            # Player notification messages
            player_notifications:
              mute: "&cDu wurdest für {duration} stummgeschaltet. Grund: {reason}"
              temporary_ban: "&cDu wurdest für {duration} von AdventureSMP gebannt. Grund: {reason}"
              permanent_ban: "&cDu wurdest permanent von AdventureSMP gebannt. Grund: {reason}"
              voice_mute: "&cDu wurdest im Voice-Chat für {duration} gemuted. Grund: {reason}"
              ip_ban: "&cDeine IP-Adresse wurde von AdventureSMP gebannt. Grund: {reason}"
            
            # Command messages
            commands:
              punish:
                no_permission: "&cDu hast keine Berechtigung für diesen Befehl."
                player_not_found: "&cSpieler '{player}' wurde nicht gefunden."
                player_offline: "&cSpieler '{player}' ist nicht online."
                punishment_applied: "&aStrafe erfolgreich angewendet: {type} für {duration}. Grund: {reason}"
                punishment_failed: "&cFehler beim Anwenden der Strafe: {error}"
                already_punished: "&cSpieler '{player}' hat bereits eine aktive {type}-Strafe."
                template_no_permission: "&cDu hast keine Berechtigung für das Template '{template}'."
                invalid_duration: "&cUngültige Dauer: '{duration}'. Beispiele: 30m, 2h, 1d, permanent"
                
              history:
                no_permission: "&cDu hast keine Berechtigung für diesen Befehl."
                no_permission_others: "&cDu hast keine Berechtigung, die Historie anderer Spieler zu sehen."
                player_not_found: "&cSpieler '{player}' wurde nicht gefunden."
                no_punishments: "&7Keine Strafen für Spieler '{player}' gefunden."
                loading: "&7Lade Strafhistorie für '{player}'..."
                
              punishadmin:
                no_permission: "&cDu hast keine Berechtigung für diesen Befehl."
                config_reloaded: "&aKonfiguration erfolgreich neu geladen."
                reload_failed: "&cFehler beim Neuladen der Konfiguration: {error}"
                stats_header: "&6=== Punishment System Statistiken ==="
                stats_total: "&7Gesamte Strafen: &e{total}"
                stats_active: "&7Aktive Strafen: &e{active}"
                stats_templates: "&7Templates: &e{templates}"
            
            # GUI messages
            gui:
              punish:
                title: "Spielerakte: {player}"
                loading: "&7Lade Spielerdaten..."
                header_info: "&7UUID: &f{uuid}|&7IP: &f{ip}|&7Strafen: &f{punishments}"
                action_mute: "&cChat Mute"
                action_voice_mute: "&6Voice Mute"
                action_ban: "&4Ban"
                manual_punishment: "&eManuelle Strafe"
                confirm_punishment: "&aStrafe ausführen"
                cancel: "&cAbbrechen"
                template_tier: "&7Stufe {tier}: &f{duration} - {reason}"
                current_tier: "&a▶ Aktuelle Stufe {tier}: &f{duration}"
                internal_note: "&7Interne Notiz (optional):"
                
              history:
                title: "Strafhistorie: {player}"
                loading: "&7Lade Historie..."
                no_entries: "&7Keine Einträge gefunden"
                entry_format: "&7#{id} &f{date} &7- &c{type} &7({duration}) von &f{staff}"
                entry_reason: "&7Grund: &f{reason}"
                entry_status_active: "&a[AKTIV]"
                entry_status_expired: "&7[ABGELAUFEN]"
                entry_status_pardoned: "&e[BEGNADIGT]"
                filter_all: "&7Alle anzeigen"
                filter_mute: "&cNur Mutes"
                filter_voice_mute: "&6Nur Voice Mutes"
                filter_ban: "&4Nur Bans"
                action_pardon: "&eAufheben"
                action_edit: "&bBearbeiten"
                action_delete: "&cLöschen"
                action_details: "&7Details"
            
            # Error messages
            errors:
              database_error: "&cDatenbankfehler: {error}"
              internal_error: "&cInterner Fehler. Bitte kontaktiere einen Administrator."
              voice_chat_unavailable: "&cVoice Chat ist nicht verfügbar."
              permission_denied: "&cZugriff verweigert."
              invalid_input: "&cUngültige Eingabe: {input}"
              player_immune: "&cDieser Spieler kann nicht bestraft werden."
              
            # Success messages
            success:
              punishment_applied: "&aStrafe erfolgreich angewendet."
              punishment_pardoned: "&aStrafe erfolgreich aufgehoben."
              punishment_edited: "&aStrafe erfolgreich bearbeitet."
              punishment_deleted: "&aStrafe erfolgreich gelöscht."
              note_added: "&aInterne Notiz hinzugefügt."
            
            # Placeholders available:
            # {player} - Player name
            # {uuid} - Player UUID
            # {ip} - Player IP address
            # {staff} - Staff member name
            # {reason} - Punishment reason
            # {duration} - Punishment duration
            # {type} - Punishment type
            # {template} - Template name
            # {tier} - Template tier
            # {date} - Date/time
            # {id} - Punishment ID
            # {error} - Error message
            """;
        
        try (FileWriter writer = new FileWriter(configFile.toFile())) {
            writer.write(defaultConfig);
        }
        
        corePlugin.getLogger().info("[PunishmentSystem] Created default message configuration.");
    }

    /**
     * Gets a message with placeholders applied.
     */
    public String getMessage(String path, String... placeholders) {
        String message = getMessageRaw(path);
        if (message == null) {
            return "&cMessage not found: " + path;
        }
        
        return MessageUtil.applyPlaceholders(message, placeholders);
    }

    /**
     * Gets a raw message without placeholder processing.
     */
    @SuppressWarnings("unchecked")
    public String getMessageRaw(String path) {
        String[] parts = path.split("\\.");
        Object current = config;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current instanceof String ? (String) current : null;
    }

    /**
     * Gets a message section as a map.
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getMessageSection(String path) {
        String[] parts = path.split("\\.");
        Object current = config;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
            } else {
                return new HashMap<>();
            }
        }
        
        return current instanceof Map ? (Map<String, Object>) current : new HashMap<>();
    }

    /**
     * Reloads the message configuration from file.
     */
    public void reload() {
        loadConfiguration();
        corePlugin.getLogger().info("[PunishmentSystem] Message configuration reloaded successfully.");
    }

    // Convenience methods for common messages
    public String getPlayerNotification(String type, String... placeholders) {
        return getMessage("player_notifications." + type, placeholders);
    }

    public String getCommandMessage(String command, String key, String... placeholders) {
        return getMessage("commands." + command + "." + key, placeholders);
    }

    public String getGuiMessage(String section, String key, String... placeholders) {
        return getMessage("gui." + section + "." + key, placeholders);
    }

    public String getErrorMessage(String key, String... placeholders) {
        return getMessage("errors." + key, placeholders);
    }

    public String getSuccessMessage(String key, String... placeholders) {
        return getMessage("success." + key, placeholders);
    }
}
