package de.dasjeff.aSMPVCore.modules.punishment.managers;

import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.ASMPVCore;
import de.dasjeff.aSMPVCore.modules.punishment.PunishmentModule;
import de.dasjeff.aSMPVCore.modules.punishment.model.Punishment;
import de.dasjeff.aSMPVCore.util.MessageUtil;

import java.util.Optional;

/**
 * Manager for voice chat related punishments.
 * Integrates with Simple Voice Chat API for voice muting functionality.
 */
public class VoiceChatManager {

    private final ASMPVCore corePlugin;
    private final PunishmentModule punishmentModule;
    private boolean voiceChatAvailable = false;

    public VoiceChatManager(ASMPVCore corePlugin, PunishmentModule punishmentModule) {
        this.corePlugin = corePlugin;
        this.punishmentModule = punishmentModule;
        
        // Check if Voice Chat API is available
        checkVoiceChatAvailability();
    }

    /**
     * Checks if Simple Voice Chat is available.
     */
    private void checkVoiceChatAvailability() {
        try {
            // Try to load the Voice Chat API class
            Class.forName("de.maxhenkel.voicechat.api.VoicechatApi");
            voiceChatAvailable = true;
            corePlugin.getLogger().info("Simple Voice Chat API detected and available.");
        } catch (ClassNotFoundException e) {
            voiceChatAvailable = false;
            corePlugin.getLogger().warn("Simple Voice Chat API not found. Voice muting will be disabled.");
        }
    }

    /**
     * Applies a voice mute punishment to a player.
     */
    public boolean applyVoiceMute(Punishment punishment) {
        if (!voiceChatAvailable) {
            corePlugin.getLogger().warn("Cannot apply voice mute - Voice Chat API not available");
            return false;
        }

        try {
            // Find the player if online
            Optional<Player> playerOpt = corePlugin.getProxyServer().getPlayer(punishment.getPlayerUuid());
            
            if (playerOpt.isPresent()) {
                Player player = playerOpt.get();
                
                // Apply voice mute via API
                boolean success = applyVoiceMuteViaAPI(player, punishment);
                
                if (success) {
                    // Create voice mute message
                    String voiceMuteMessage = createVoiceMuteMessage(punishment);
                    
                    // Send notification to player
                    player.sendMessage(MessageUtil.formatLegacy(voiceMuteMessage));
                    
                    corePlugin.getLogger().info("Voice muted player {} ({}): {}", 
                        player.getUsername(), punishment.getPlayerUuid(), punishment.getReason());
                } else {
                    corePlugin.getLogger().error("Failed to apply voice mute via API for player {}", 
                        player.getUsername());
                    return false;
                }
            } else {
                corePlugin.getLogger().info("Applied voice mute to offline player {}: {}", 
                    punishment.getPlayerUuid(), punishment.getReason());
            }

            // Send cross-server notification
            sendCrossServerVoiceMuteNotification(punishment);

            return true;

        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to apply voice mute punishment", e);
            return false;
        }
    }

    /**
     * Removes a voice mute from a player.
     */
    public boolean removeVoiceMute(java.util.UUID playerUuid) {
        if (!voiceChatAvailable) {
            return false;
        }

        try {
            Optional<Player> playerOpt = corePlugin.getProxyServer().getPlayer(playerUuid);
            
            if (playerOpt.isPresent()) {
                Player player = playerOpt.get();
                return removeVoiceMuteViaAPI(player);
            }
            
            return true; // Consider success if player is offline
            
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to remove voice mute", e);
            return false;
        }
    }

    /**
     * Checks if a player is currently voice muted.
     */
    public boolean isVoiceMuted(java.util.UUID playerUuid) {
        try {
            Optional<Punishment> voiceMute = punishmentModule.getDataAccessor().getActiveVoiceMute(playerUuid);
            if (voiceMute.isPresent()) {
                Punishment punishment = voiceMute.get();
                
                // Check if voice mute has expired
                if (punishment.isExpired()) {
                    // Mark as expired and remove voice mute
                    punishmentModule.getDataAccessor().updatePunishmentStatus(
                        punishment.getId(), 
                        de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentStatus.EXPIRED
                    );
                    removeVoiceMute(playerUuid);
                    return false;
                }
                
                return true;
            }
            return false;
        } catch (Exception e) {
            corePlugin.getLogger().error("Error checking voice mute status", e);
            return false;
        }
    }

    /**
     * Gets the active voice mute for a player, if any.
     */
    public Optional<Punishment> getActiveVoiceMute(java.util.UUID playerUuid) {
        return punishmentModule.getDataAccessor().getActiveVoiceMute(playerUuid);
    }

    /**
     * Applies voice mute via the Voice Chat API.
     */
    private boolean applyVoiceMuteViaAPI(Player player, Punishment punishment) {
        try {
            // TODO: Implement actual Voice Chat API integration
            // This is a placeholder for the actual API calls
            
            /*
            VoicechatApi api = VoicechatApi.instance();
            if (api != null) {
                VoicechatServerApi serverApi = api.getServerApi();
                if (serverApi != null) {
                    // Mute the player
                    serverApi.mutePlayer(player.getUniqueId());
                    return true;
                }
            }
            */
            
            // For now, just log that we would apply the voice mute
            corePlugin.getLogger().info("Would apply voice mute via API to player {}", player.getUsername());
            return true;
            
        } catch (Exception e) {
            corePlugin.getLogger().error("Error applying voice mute via API", e);
            return false;
        }
    }

    /**
     * Removes voice mute via the Voice Chat API.
     */
    private boolean removeVoiceMuteViaAPI(Player player) {
        try {
            // TODO: Implement actual Voice Chat API integration
            // This is a placeholder for the actual API calls
            
            /*
            VoicechatApi api = VoicechatApi.instance();
            if (api != null) {
                VoicechatServerApi serverApi = api.getServerApi();
                if (serverApi != null) {
                    // Unmute the player
                    serverApi.unmutePlayer(player.getUniqueId());
                    return true;
                }
            }
            */
            
            // For now, just log that we would remove the voice mute
            corePlugin.getLogger().info("Would remove voice mute via API from player {}", player.getUsername());
            return true;
            
        } catch (Exception e) {
            corePlugin.getLogger().error("Error removing voice mute via API", e);
            return false;
        }
    }

    /**
     * Creates a voice mute message for the player.
     */
    private String createVoiceMuteMessage(Punishment punishment) {
        return punishmentModule.getMessageConfig().getPlayerNotification("voice_mute",
            "{reason}", punishment.getReason(),
            "{duration}", punishment.getFormattedDuration());
    }

    /**
     * Sends cross-server notification about the voice mute.
     */
    private void sendCrossServerVoiceMuteNotification(Punishment punishment) {
        try {
            // TODO: Implement cross-server messaging for voice mutes
            // This would notify all Paper servers about the voice mute
            corePlugin.getLogger().debug("Cross-server voice mute notification sent for punishment {}", punishment.getId());
        } catch (Exception e) {
            corePlugin.getLogger().error("Failed to send cross-server voice mute notification", e);
        }
    }

    /**
     * Checks if Voice Chat is available.
     */
    public boolean isVoiceChatAvailable() {
        return voiceChatAvailable;
    }

    /**
     * Shuts down the voice chat manager.
     */
    public void shutdown() {
        corePlugin.getLogger().info("VoiceChatManager shutting down...");
        // TODO: Cleanup any resources if needed
    }
}
