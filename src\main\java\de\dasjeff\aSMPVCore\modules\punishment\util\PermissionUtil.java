package de.dasjeff.aSMPVCore.modules.punishment.util;

import com.velocitypowered.api.permission.Tristate;
import com.velocitypowered.api.proxy.Player;
import de.dasjeff.aSMPVCore.modules.punishment.model.PunishmentType;

/**
 * Utility class for handling punishment-related permissions.
 * Provides centralized permission checking for the punishment system.
 */
public class PermissionUtil {

    // Base permission node
    public static final String BASE_PERMISSION = "asmp.vcore.punishment";

    // Core permissions
    public static final String ACCESS = BASE_PERMISSION + ".access";
    public static final String HISTORY_VIEW_OTHERS = BASE_PERMISSION + ".history.viewothers";
    public static final String PARDON = BASE_PERMISSION + ".pardon";
    public static final String EDIT = BASE_PERMISSION + ".edit";
    public static final String DELETE = BASE_PERMISSION + ".delete";
    public static final String VIEW_INTERNAL_NOTES = BASE_PERMISSION + ".viewinternalnotes";
    public static final String ADD_INTERNAL_NOTES = BASE_PERMISSION + ".addinternalnotes";
    public static final String CONFIG_RELOAD = BASE_PERMISSION + ".config.reload";

    // Template permissions
    public static final String TEMPLATE_BASE = BASE_PERMISSION + ".template";
    
    // Custom punishment permissions
    public static final String CUSTOM_MUTE = BASE_PERMISSION + ".custom.mute";
    public static final String CUSTOM_VOICE_MUTE = BASE_PERMISSION + ".custom.voicemute";
    public static final String CUSTOM_BAN = BASE_PERMISSION + ".custom.ban";

    // Group permissions
    public static final String TEMPLATE_CHAT_ALL = TEMPLATE_BASE + ".chat.*";
    public static final String TEMPLATE_VOICE_ALL = TEMPLATE_BASE + ".voicemute.*";
    public static final String TEMPLATE_BAN_ALL = TEMPLATE_BASE + ".ban.*";
    public static final String TEMPLATE_ALL = TEMPLATE_BASE + ".*";

    // Admin permissions
    public static final String ADMIN_ALL = BASE_PERMISSION + ".*";

    /**
     * Checks if a player has basic punishment access.
     */
    public boolean hasAccess(Player player) {
        return hasPermission(player, ACCESS);
    }

    /**
     * Checks if a player can view other players' punishment history.
     */
    public boolean canViewOthersHistory(Player player) {
        return hasPermission(player, HISTORY_VIEW_OTHERS);
    }

    /**
     * Checks if a player can pardon punishments.
     */
    public boolean canPardon(Player player) {
        return hasPermission(player, PARDON);
    }

    /**
     * Checks if a player can edit punishments.
     */
    public boolean canEdit(Player player) {
        return hasPermission(player, EDIT);
    }

    /**
     * Checks if a player can delete punishments.
     */
    public boolean canDelete(Player player) {
        return hasPermission(player, DELETE);
    }

    /**
     * Checks if a player can view internal notes.
     */
    public boolean canViewInternalNotes(Player player) {
        return hasPermission(player, VIEW_INTERNAL_NOTES);
    }

    /**
     * Checks if a player can add internal notes.
     */
    public boolean canAddInternalNotes(Player player) {
        return hasPermission(player, ADD_INTERNAL_NOTES);
    }

    /**
     * Checks if a player can reload the configuration.
     */
    public boolean canReloadConfig(Player player) {
        return hasPermission(player, CONFIG_RELOAD);
    }

    /**
     * Checks if a player can use a specific template.
     */
    public boolean canUseTemplate(Player player, String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            return false;
        }

        String specificPermission = TEMPLATE_BASE + "." + templateName.toLowerCase();
        
        // Check specific template permission first
        if (hasPermission(player, specificPermission)) {
            return true;
        }

        // Check group permissions based on template name patterns
        if (templateName.toLowerCase().startsWith("chat_")) {
            return hasPermission(player, TEMPLATE_CHAT_ALL);
        } else if (templateName.toLowerCase().startsWith("voice_")) {
            return hasPermission(player, TEMPLATE_VOICE_ALL);
        } else if (templateName.toLowerCase().startsWith("ban_")) {
            return hasPermission(player, TEMPLATE_BAN_ALL);
        }

        // Check wildcard permissions
        return hasPermission(player, TEMPLATE_ALL);
    }

    /**
     * Checks if a player can apply custom punishments of a specific type.
     */
    public boolean canUseCustomPunishment(Player player, PunishmentType type) {
        return switch (type) {
            case MUTE -> hasPermission(player, CUSTOM_MUTE);
            case VOICE_MUTE -> hasPermission(player, CUSTOM_VOICE_MUTE);
            case BAN -> hasPermission(player, CUSTOM_BAN);
        };
    }

    /**
     * Checks if a player has any template permissions for a specific punishment type.
     */
    public boolean hasAnyTemplatePermission(Player player, PunishmentType type) {
        return switch (type) {
            case MUTE -> hasPermission(player, TEMPLATE_CHAT_ALL) || hasPermission(player, TEMPLATE_ALL);
            case VOICE_MUTE -> hasPermission(player, TEMPLATE_VOICE_ALL) || hasPermission(player, TEMPLATE_ALL);
            case BAN -> hasPermission(player, TEMPLATE_BAN_ALL) || hasPermission(player, TEMPLATE_ALL);
        };
    }

    /**
     * Checks if a player is immune to punishments (has admin permissions).
     */
    public boolean isImmune(Player player) {
        return hasPermission(player, ADMIN_ALL);
    }

    /**
     * Gets the highest permission level for template access.
     * Returns the most permissive template permission the player has.
     */
    public TemplatePermissionLevel getTemplatePermissionLevel(Player player) {
        if (hasPermission(player, TEMPLATE_ALL)) {
            return TemplatePermissionLevel.ALL;
        }
        
        boolean hasChat = hasPermission(player, TEMPLATE_CHAT_ALL);
        boolean hasVoice = hasPermission(player, TEMPLATE_VOICE_ALL);
        boolean hasBan = hasPermission(player, TEMPLATE_BAN_ALL);
        
        if (hasChat && hasVoice && hasBan) {
            return TemplatePermissionLevel.ALL;
        } else if (hasChat || hasVoice || hasBan) {
            return TemplatePermissionLevel.PARTIAL;
        } else {
            return TemplatePermissionLevel.SPECIFIC_ONLY;
        }
    }

    /**
     * Helper method to check permissions with proper fallback handling.
     */
    private boolean hasPermission(Player player, String permission) {
        if (player == null || permission == null) {
            return false;
        }

        Tristate result = player.getPermissionValue(permission);
        return result == Tristate.TRUE;
    }

    /**
     * Generates a template-specific permission node.
     */
    public static String getTemplatePermission(String templateName) {
        if (templateName == null || templateName.isEmpty()) {
            return null;
        }
        return TEMPLATE_BASE + "." + templateName.toLowerCase();
    }

    /**
     * Checks if a permission string is a valid punishment permission.
     */
    public static boolean isValidPunishmentPermission(String permission) {
        if (permission == null || permission.isEmpty()) {
            return false;
        }
        
        return permission.startsWith(BASE_PERMISSION);
    }

    /**
     * Enumeration of template permission levels.
     */
    public enum TemplatePermissionLevel {
        /**
         * Player has access to all templates via wildcard permission.
         */
        ALL,
        
        /**
         * Player has access to some template groups (chat.*, ban.*, etc.).
         */
        PARTIAL,
        
        /**
         * Player only has access to specific templates.
         */
        SPECIFIC_ONLY,
        
        /**
         * Player has no template permissions.
         */
        NONE
    }
}
