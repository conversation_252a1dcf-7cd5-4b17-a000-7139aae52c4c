package de.dasjeff.aSMPVCore.util;

import de.dasjeff.aSMPVCore.managers.ConfigManager.ConfigWrapper;

/**
 * Utility class for validating configuration values.
 * Ensures configuration values are within acceptable ranges and formats.
 */
public final class ConfigValidator {

    private ConfigValidator() {
        // Utility class - no instantiation
    }

    /**
     * Validates database configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validateDatabaseConfig(ConfigWrapper config) {
        // Validate host
        String host = config.getString("database.host", "localhost");
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("Database host cannot be empty");
        }

        // Validate port
        int port = config.getInt("database.port", 3306);
        if (port <= 0 || port > 65535) {
            throw new IllegalArgumentException("Database port must be between 1 and 65535, got: " + port);
        }

        // Validate database name
        String database = config.getString("database.database", "asmp_vcore");
        if (database == null || database.trim().isEmpty()) {
            throw new IllegalArgumentException("Database name cannot be empty");
        }
        if (!database.matches("^[a-zA-Z0-9_]+$")) {
            throw new IllegalArgumentException("Database name contains invalid characters: " + database);
        }

        // Validate username
        String username = config.getString("database.username", "root");
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Database username cannot be empty");
        }

        // Validate pool size
        int poolSize = config.getInt("database.pool_size", 10);
        if (poolSize <= 0 || poolSize > 100) {
            throw new IllegalArgumentException("Database pool size must be between 1 and 100, got: " + poolSize);
        }

        // Validate timeouts
        long connectionTimeout = config.getLong("database.connection_timeout", 30000);
        if (connectionTimeout < 1000 || connectionTimeout > 300000) {
            throw new IllegalArgumentException("Database connection timeout must be between 1000ms and 300000ms, got: " + connectionTimeout);
        }

        long idleTimeout = config.getLong("database.idle_timeout", 600000);
        if (idleTimeout < 10000 || idleTimeout > 3600000) {
            throw new IllegalArgumentException("Database idle timeout must be between 10000ms and 3600000ms, got: " + idleTimeout);
        }

        long maxLifetime = config.getLong("database.max_lifetime", 1800000);
        if (maxLifetime < 30000 || maxLifetime > 7200000) {
            throw new IllegalArgumentException("Database max lifetime must be between 30000ms and 7200000ms, got: " + maxLifetime);
        }
    }

    /**
     * Validates Redis configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validateRedisConfig(ConfigWrapper config) {
        if (!config.getBoolean("redis.enabled", true)) {
            return; // Skip validation if Redis is disabled
        }

        // Validate host
        String host = config.getString("redis.host", "localhost");
        if (host == null || host.trim().isEmpty()) {
            throw new IllegalArgumentException("Redis host cannot be empty");
        }

        // Validate port
        int port = config.getInt("redis.port", 6379);
        if (port <= 0 || port > 65535) {
            throw new IllegalArgumentException("Redis port must be between 1 and 65535, got: " + port);
        }

        // Validate database number
        int database = config.getInt("redis.database", 0);
        if (database < 0 || database > 15) {
            throw new IllegalArgumentException("Redis database must be between 0 and 15, got: " + database);
        }

        // Validate timeout
        int timeout = config.getInt("redis.timeout", 2000);
        if (timeout < 100 || timeout > 30000) {
            throw new IllegalArgumentException("Redis timeout must be between 100ms and 30000ms, got: " + timeout);
        }

        // Validate pool settings
        int poolMaxTotal = config.getInt("redis.pool_max_total", 20);
        if (poolMaxTotal <= 0 || poolMaxTotal > 200) {
            throw new IllegalArgumentException("Redis pool max total must be between 1 and 200, got: " + poolMaxTotal);
        }

        int poolMaxIdle = config.getInt("redis.pool_max_idle", 10);
        if (poolMaxIdle <= 0 || poolMaxIdle > poolMaxTotal) {
            throw new IllegalArgumentException("Redis pool max idle must be between 1 and " + poolMaxTotal + ", got: " + poolMaxIdle);
        }

        int poolMinIdle = config.getInt("redis.pool_min_idle", 2);
        if (poolMinIdle < 0 || poolMinIdle > poolMaxIdle) {
            throw new IllegalArgumentException("Redis pool min idle must be between 0 and " + poolMaxIdle + ", got: " + poolMinIdle);
        }
    }

    /**
     * Validates cache configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validateCacheConfig(ConfigWrapper config) {
        // Validate default max size
        int defaultMaxSize = config.getInt("cache.default_max_size", 1000);
        if (defaultMaxSize <= 0 || defaultMaxSize > 100000) {
            throw new IllegalArgumentException("Cache default max size must be between 1 and 100000, got: " + defaultMaxSize);
        }

        // Validate default expire time
        int defaultExpireMinutes = config.getInt("cache.default_expire_minutes", 30);
        if (defaultExpireMinutes <= 0 || defaultExpireMinutes > 1440) {
            throw new IllegalArgumentException("Cache default expire minutes must be between 1 and 1440, got: " + defaultExpireMinutes);
        }
    }

    /**
     * Validates security configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validateSecurityConfig(ConfigWrapper config) {
        // Validate command cooldown
        long commandCooldown = config.getLong("security.command_cooldown_ms", 100);
        if (commandCooldown < 0 || commandCooldown > 10000) {
            throw new IllegalArgumentException("Security command cooldown must be between 0ms and 10000ms, got: " + commandCooldown);
        }

        // Validate max async operations
        int maxAsyncOps = config.getInt("security.max_async_operations_per_player", 10);
        if (maxAsyncOps <= 0 || maxAsyncOps > 100) {
            throw new IllegalArgumentException("Security max async operations per player must be between 1 and 100, got: " + maxAsyncOps);
        }

        // Validate rate limit requests per minute
        int rateLimitRequests = config.getInt("security.rate_limit_requests_per_minute", 60);
        if (rateLimitRequests <= 0 || rateLimitRequests > 1000) {
            throw new IllegalArgumentException("Security rate limit requests per minute must be between 1 and 1000, got: " + rateLimitRequests);
        }
    }

    /**
     * Validates webpanel configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validateWebpanelConfig(ConfigWrapper config) {
        if (!config.getBoolean("webpanel.enabled", false)) {
            return; // Skip validation if webpanel is disabled
        }

        // Validate API key
        String apiKey = config.getString("webpanel.api_key", "change_this_api_key");
        if (apiKey == null || apiKey.trim().isEmpty()) {
            throw new IllegalArgumentException("Webpanel API key cannot be empty");
        }
        if (apiKey.equals("change_this_api_key")) {
            throw new IllegalArgumentException("Webpanel API key must be changed from default value");
        }
        if (apiKey.length() < 16) {
            throw new IllegalArgumentException("Webpanel API key must be at least 16 characters long");
        }

        // Validate port (if configured)
        int port = config.getInt("webpanel.port", 8080);
        if (port <= 1024 || port > 65535) {
            throw new IllegalArgumentException("Webpanel port must be between 1025 and 65535, got: " + port);
        }
    }

    /**
     * Validates performance configuration.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if configuration is invalid.
     */
    public static void validatePerformanceConfig(ConfigWrapper config) {
        // Validate async thread pool size
        int threadPoolSize = config.getInt("performance.async_thread_pool_size", 4);
        if (threadPoolSize <= 0 || threadPoolSize > 32) {
            throw new IllegalArgumentException("Performance async thread pool size must be between 1 and 32, got: " + threadPoolSize);
        }

        // Validate batch size
        int batchSize = config.getInt("performance.batch_size", 100);
        if (batchSize <= 0 || batchSize > 10000) {
            throw new IllegalArgumentException("Performance batch size must be between 1 and 10000, got: " + batchSize);
        }

        // Validate cleanup intervals
        int cacheCleanupInterval = config.getInt("performance.cache_cleanup_interval_minutes", 5);
        if (cacheCleanupInterval <= 0 || cacheCleanupInterval > 60) {
            throw new IllegalArgumentException("Performance cache cleanup interval must be between 1 and 60 minutes, got: " + cacheCleanupInterval);
        }

        int securityCleanupInterval = config.getInt("performance.security_cleanup_interval_minutes", 2);
        if (securityCleanupInterval <= 0 || securityCleanupInterval > 30) {
            throw new IllegalArgumentException("Performance security cleanup interval must be between 1 and 30 minutes, got: " + securityCleanupInterval);
        }
    }

    /**
     * Validates all configuration sections.
     * @param config The configuration to validate.
     * @throws IllegalArgumentException if any configuration is invalid.
     */
    public static void validateAllConfig(ConfigWrapper config) {
        try {
            validateDatabaseConfig(config);
            validateRedisConfig(config);
            validateCacheConfig(config);
            validateSecurityConfig(config);
            validateWebpanelConfig(config);
            validatePerformanceConfig(config);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Configuration validation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Validates a string for SQL injection and other security issues.
     * @param input The input string to validate.
     * @param fieldName The name of the field being validated.
     * @throws IllegalArgumentException if the input is unsafe.
     */
    public static void validateSecureInput(String input, String fieldName) {
        if (input == null) {
            return; // Null is acceptable for optional fields
        }

        // Check for SQL injection patterns
        String lowerInput = input.toLowerCase();
        String[] dangerousPatterns = {
            "select", "insert", "update", "delete", "drop", "create", "alter",
            "union", "exec", "execute", "script", "javascript", "vbscript",
            "onload", "onerror", "onclick", "<script", "</script>", "eval(",
            "--", "/*", "*/", "xp_", "sp_"
        };

        for (String pattern : dangerousPatterns) {
            if (lowerInput.contains(pattern)) {
                throw new IllegalArgumentException(fieldName + " contains potentially dangerous content: " + pattern);
            }
        }

        // Check for suspicious characters
        char[] suspiciousChars = {'\'', '"', ';', '<', '>', '&', '%', '\\', '\0'};
        for (char c : suspiciousChars) {
            if (input.indexOf(c) != -1) {
                throw new IllegalArgumentException(fieldName + " contains suspicious character: " + c);
            }
        }
    }
}
